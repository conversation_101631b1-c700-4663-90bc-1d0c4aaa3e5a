<div class="settings-container">
  <!-- Header Section -->
  <div class="settings-header">
    <div class="header-content">
      <h1 class="page-title">
        <mat-icon>settings</mat-icon>
        Church Settings
      </h1>
      <p class="page-subtitle">Manage your church configuration and preferences</p>
    </div>

    <div class="header-actions">
      <button mat-icon-button (click)="onRefreshData()" [disabled]="isLoading()" matTooltip="Refresh">
        <mat-icon>refresh</mat-icon>
      </button>
    </div>
  </div>

  @if (isLoading()) {
  <div class="loading-container">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Loading church settings...</p>
  </div>
  } @else if (church()) {
  <!-- Settings Tabs -->
  <mat-card class="settings-card">
    <mat-tab-group class="settings-tabs" animationDuration="300ms">

      <!-- Church Information Tab -->
      <mat-tab label="Church Information">
        <div class="tab-content">
          <form [formGroup]="churchInfoForm" (ngSubmit)="onSaveChurchInfo()">
            <div class="form-section">
              <h3>Basic Information</h3>
              <div class="form-grid">
                <mat-form-field appearance="outline">
                  <mat-label>Church Name</mat-label>
                  <input matInput formControlName="name" placeholder="Enter church name">
                  <mat-error *ngIf="churchInfoForm.get('name')?.hasError('required')">
                    Church name is required
                  </mat-error>
                  <mat-error *ngIf="churchInfoForm.get('name')?.hasError('minlength')">
                    Church name must be at least 2 characters
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Phone Number</mat-label>
                  <input matInput formControlName="phone" placeholder="Enter phone number">
                  <mat-icon matSuffix>phone</mat-icon>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Email Address</mat-label>
                  <input matInput formControlName="email" type="email" placeholder="Enter email address">
                  <mat-icon matSuffix>email</mat-icon>
                  <mat-error *ngIf="churchInfoForm.get('email')?.hasError('email')">
                    Please enter a valid email address
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Description</mat-label>
                  <textarea matInput formControlName="description" rows="3"
                    placeholder="Brief description of your church"></textarea>
                </mat-form-field>
              </div>
            </div>

            <div class="form-actions">
              <button mat-button type="button" (click)="onResetForm('churchInfo')">Reset</button>
              <button mat-raised-button color="primary" type="submit" [disabled]="!churchInfoForm.valid || isSaving()">
                @if (isSaving()) {
                <mat-spinner diameter="20"></mat-spinner>
                }
                Save Changes
              </button>
            </div>
          </form>
        </div>
      </mat-tab>

      <!-- Admin & Contact Tab -->
      <mat-tab label="Admin & Contact">
        <div class="tab-content">
          <form [formGroup]="adminContactForm" (ngSubmit)="onSaveAdminContact()">
            <div class="form-section">
              <h3>Administrator Information</h3>
              <div class="form-grid">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Admin Name</mat-label>
                  <input matInput formControlName="adminName" placeholder="Enter admin name">
                  <mat-icon matSuffix>person</mat-icon>
                  <mat-error *ngIf="adminContactForm.get('adminName')?.hasError('required')">
                    Admin name is required
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Admin Phone</mat-label>
                  <input matInput formControlName="adminPhone" placeholder="Enter admin phone">
                  <mat-icon matSuffix>phone</mat-icon>
                  <mat-error *ngIf="adminContactForm.get('adminPhone')?.hasError('required')">
                    Admin phone is required
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Admin Email</mat-label>
                  <input matInput formControlName="adminEmail" type="email" placeholder="Enter admin email">
                  <mat-icon matSuffix>email</mat-icon>
                  <mat-error *ngIf="adminContactForm.get('adminEmail')?.hasError('required')">
                    Admin email is required
                  </mat-error>
                  <mat-error *ngIf="adminContactForm.get('adminEmail')?.hasError('email')">
                    Please enter a valid email address
                  </mat-error>
                </mat-form-field>
              </div>
            </div>

            <div class="form-actions">
              <button mat-button type="button" (click)="onResetForm('adminContact')">Reset</button>
              <button mat-raised-button color="primary" type="submit"
                [disabled]="!adminContactForm.valid || isSaving()">
                @if (isSaving()) {
                <mat-spinner diameter="20"></mat-spinner>
                }
                Save Changes
              </button>
            </div>
          </form>
        </div>
      </mat-tab>

      <!-- Service Times Tab -->
      <mat-tab label="Service Times">
        <div class="tab-content">
          <form [formGroup]="serviceTimesForm" (ngSubmit)="onSaveServiceTimes()">
            <div class="form-section">
              <div class="section-header">
                <h3>Service Schedule</h3>
                <button mat-icon-button type="button" (click)="addServiceTime()" matTooltip="Add Service">
                  <mat-icon>add</mat-icon>
                </button>
              </div>

              <div formArrayName="serviceTimes" class="service-times-list">
                @for (serviceTime of serviceTimesArray.controls; track $index) {
                <mat-card class="service-time-card" [formGroupName]="$index">
                  <mat-card-content>
                    <div class="service-time-header">
                      <h4>Service {{ $index + 1 }}</h4>
                      @if (serviceTimesArray.length > 1) {
                      <button mat-icon-button type="button" (click)="removeServiceTime($index)"
                        matTooltip="Remove Service" class="remove-btn">
                        <mat-icon>delete</mat-icon>
                      </button>
                      }
                    </div>

                    <div class="service-time-form">
                      <mat-form-field appearance="outline">
                        <mat-label>Service Name</mat-label>
                        <input matInput formControlName="name" placeholder="e.g., Morning Service">
                        @if (serviceTime.get('name')?.invalid && serviceTime.get('name')?.touched) {
                        <mat-error>
                          @if (serviceTime.get('name')?.errors?.['required']) {
                          Service name is required
                          }
                          @if (serviceTime.get('name')?.errors?.['minlength']) {
                          Service name must be at least 2 characters
                          }
                        </mat-error>
                        }
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Day of Week (for recurring services)</mat-label>
                        <mat-select formControlName="day">
                          <mat-option value="">Select day (optional)</mat-option>
                          @for (day of dayOptions; track day.value) {
                          <mat-option [value]="day.value">{{ day.label }}</mat-option>
                          }
                        </mat-select>
                      </mat-form-field>

                      <mat-form-field appearance="outline">
                        <mat-label>Specific Date (for one-time events)</mat-label>
                        <input matInput [matDatepicker]="picker" formControlName="date"
                          placeholder="Select date (optional)">
                        <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                      </mat-form-field>

                      <div class="time-fields">
                        <mat-form-field appearance="outline">
                          <mat-label>Start Time</mat-label>
                          <input matInput type="time" formControlName="startTime">
                          @if (serviceTime.get('startTime')?.invalid && serviceTime.get('startTime')?.touched) {
                          <mat-error>Start time is required</mat-error>
                          }
                        </mat-form-field>

                        <mat-form-field appearance="outline">
                          <mat-label>End Time</mat-label>
                          <input matInput type="time" formControlName="endTime">
                          @if (serviceTime.get('endTime')?.invalid && serviceTime.get('endTime')?.touched) {
                          <mat-error>End time is required</mat-error>
                          }
                        </mat-form-field>
                      </div>
                    </div>
                  </mat-card-content>
                </mat-card>
                }
              </div>
            </div>

            <div class="form-actions">
              <button mat-button type="button" (click)="onResetForm('serviceTimes')">Reset</button>
              <button mat-raised-button color="primary" type="submit"
                [disabled]="!serviceTimesForm.valid || isSaving()">
                @if (isSaving()) {
                <mat-spinner diameter="20"></mat-spinner>
                }
                Save Changes
              </button>
            </div>
          </form>
        </div>
      </mat-tab>

      <!-- Location & Branding Tab -->
      <mat-tab label="Location & Branding">
        <div class="tab-content">
          <!-- Church Logo Section -->
          <div class="form-section">
            <h3>Church Logo</h3>
            <p class="section-description">Upload your church logo (coming soon)</p>
            <mat-card>
              <mat-card-content>
                <p>Logo upload functionality will be available soon.</p>
              </mat-card-content>
            </mat-card>
          </div>

          <!-- Location Section -->
          <div class="form-section">
            <h3>Church Location</h3>
            <p class="section-description">
              Set your church's location for attendance tracking.
            </p>

            <form [formGroup]="locationForm" (ngSubmit)="onSaveLocation()">
              <div class="form-grid">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Address</mat-label>
                  <textarea matInput formControlName="address" rows="2" placeholder="Enter church address"></textarea>
                  <mat-icon matSuffix>location_on</mat-icon>
                  <mat-error *ngIf="locationForm.get('address')?.hasError('required')">
                    Address is required
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Latitude</mat-label>
                  <input matInput type="number" formControlName="latitude" placeholder="e.g., 5.6037" step="0.000001">
                  <mat-error *ngIf="locationForm.get('latitude')?.hasError('required')">
                    Latitude is required
                  </mat-error>
                  <mat-error
                    *ngIf="locationForm.get('latitude')?.hasError('min') || locationForm.get('latitude')?.hasError('max')">
                    Latitude must be between -90 and 90
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Longitude</mat-label>
                  <input matInput type="number" formControlName="longitude" placeholder="e.g., -0.1870" step="0.000001">
                  <mat-error *ngIf="locationForm.get('longitude')?.hasError('required')">
                    Longitude is required
                  </mat-error>
                  <mat-error
                    *ngIf="locationForm.get('longitude')?.hasError('min') || locationForm.get('longitude')?.hasError('max')">
                    Longitude must be between -180 and 180
                  </mat-error>
                </mat-form-field>
              </div>

              <!-- Geofence Settings -->
              <div class="geofence-section">
                <h4>Attendance Geofence</h4>
                <p class="section-description">
                  Set the radius around your church where members can check in for attendance.
                </p>

                <div class="slider-field">
                  <label class="slider-label">Geofence Radius</label>
                  <mat-slider min="10" max="1000" step="10" discrete>
                    <input matSliderThumb formControlName="geofenceRadius">
                  </mat-slider>
                  <div class="slider-hint">{{ formatGeofenceRadius(locationForm.get('geofenceRadius')?.value || 100) }}
                  </div>
                </div>
              </div>

              <div class="form-actions">
                <button mat-button type="button" (click)="onResetForm('location')">Reset</button>
                <button mat-raised-button color="primary" type="submit" [disabled]="!locationForm.valid || isSaving()">
                  @if (isSaving()) {
                  <mat-spinner diameter="20"></mat-spinner>
                  }
                  Save Location & Address
                </button>
              </div>
            </form>
          </div>
        </div>
      </mat-tab>

    </mat-tab-group>
  </mat-card>
  } @else {
  <div class="empty-state">
    <mat-icon>church</mat-icon>
    <h3>No Church Found</h3>
    <p>No church is associated with your account.</p>
  </div>
  }
</div>