import { Component, OnInit, OnDestroy, computed, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subject, takeUntil, combineLatest, of } from 'rxjs';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';

import { AuthService } from '../../core/services/auth.service';
import { ChurchService } from '../../core/services/church.service';
import { AdminService } from '../../core/services/admin.service';
import { MembersService } from '../../core/services/members.service';
import { AttendanceService } from '../../core/services/attendance.service';
import { User } from '../../shared/models/user.model';
import { Church } from '../../shared/models/church.model';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatChipsModule
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss'
})
export class DashboardComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Signals for reactive UI
  isLoading = signal<boolean>(true);
  error = signal<string | null>(null);

  // Data signals
  currentUser = signal<User | null>(null);
  currentChurch = signal<Church | null>(null);
  dashboardStats = signal<any>({
    totalMembers: 0,
    todayAttendance: 0,
    weeklyAttendance: 0,
    attendanceRate: 0,
    recentActivity: []
  });

  // Computed values
  isChurchAdmin = computed(() => {
    const user = this.currentUser();
    return user && (user.role === 'admin' || user.role === 'pastor' || user.role === 'super_admin');
  });

  quickActions = computed(() => {
    const user = this.currentUser();
    const isAdmin = this.isChurchAdmin();
    const isSuperAdmin = user?.role === 'super_admin';

    if (isSuperAdmin) {
      return [
        { title: 'Manage Churches', icon: 'church', route: '/super-admin/churches', available: true },
        { title: 'System Users', icon: 'people', route: '/super-admin/users', available: true },
        { title: 'System Reports', icon: 'assessment', route: '/super-admin/reports', available: true },
        { title: 'System Settings', icon: 'settings', route: '/super-admin/settings', available: true }
      ];
    }

    return [
      { title: 'View Attendance', icon: 'people', route: '/attendance', available: true },
      { title: 'Manage Members', icon: 'group', route: '/members', available: isAdmin },
      { title: 'Generate Reports', icon: 'assessment', route: '/reports', available: isAdmin },
      { title: 'Church Settings', icon: 'settings', route: '/settings', available: isAdmin }
    ].filter(action => action.available);
  });

  constructor(
    private authService: AuthService,
    private churchService: ChurchService,
    private adminService: AdminService,
    private membersService: MembersService,
    private attendanceService: AttendanceService,
    private router: Router
  ) { }

  ngOnInit(): void {
    // Subscribe to user changes
    this.authService.user$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(user => {
      this.currentUser.set(user);
      if (user) {
        this.loadDashboardData();
      } else {
        this.router.navigate(['/auth/login']);
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadDashboardData(): void {
    const user = this.currentUser();

    if (!user) {
      this.error.set('User not authenticated. Please log in again.');
      this.isLoading.set(false);
      return;
    }

    // Super admin doesn't need a church association
    if (user.role === 'super_admin') {
      this.loadSuperAdminDashboard();
      return;
    }

    if (!user.churchId) {
      this.error.set('No church associated with your account. Please contact your administrator.');
      this.isLoading.set(false);
      return;
    }

    this.isLoading.set(true);
    this.error.set(null);

    // Load church data and real dashboard stats
    combineLatest([
      this.churchService.getChurch(user.churchId),
      this.membersService.getChurchMembers(user.churchId),
      this.attendanceService.getLiveAttendance(user.churchId),
      this.membersService.getChurchMemberStats(user.churchId)
    ]).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: ([church, members, liveAttendance, memberStats]) => {
        this.currentChurch.set(church);

        // Calculate real dashboard statistics
        const todayAttendance = liveAttendance.totalCheckedIn || 0;
        const totalMembers = members.length;
        const weeklyAttendance = this.calculateWeeklyAttendance(liveAttendance);
        const attendanceRate = totalMembers > 0 ? Math.round((todayAttendance / totalMembers) * 100) : 0;

        this.dashboardStats.set({
          totalMembers,
          todayAttendance,
          weeklyAttendance,
          attendanceRate,
          recentActivity: this.generateRecentActivity(members, liveAttendance)
        });

        console.log('📊 Dashboard stats loaded:', {
          totalMembers,
          todayAttendance,
          weeklyAttendance,
          attendanceRate
        });

        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('Error loading dashboard data:', error);
        this.error.set('Failed to load dashboard data. Please try again.');
        this.isLoading.set(false);
      }
    });
  }

  private loadSuperAdminDashboard(): void {
    this.isLoading.set(true);
    this.error.set(null);

    // Load super admin dashboard data (all churches overview)
    this.churchService.getChurches().pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (churches) => {
        // Set super admin dashboard stats
        this.dashboardStats.set({
          totalChurches: churches.length,
          totalMembers: 0, // TODO: Calculate from all churches
          activeChurches: churches.filter(c => c.settings?.allowSelfRegistration).length,
          recentActivity: []
        });

        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('Error loading super admin dashboard:', error);
        this.error.set('Failed to load dashboard data. Please try again.');
        this.isLoading.set(false);
      }
    });
  }

  // Action handlers
  onQuickAction(route: string): void {
    this.router.navigate([route]);
  }

  onRetryLoadData(): void {
    this.loadDashboardData();
  }

  onRefreshData(): void {
    this.loadDashboardData();
  }

  onViewAttendance(): void {
    this.router.navigate(['/attendance']);
  }

  onManageMembers(): void {
    this.router.navigate(['/members']);
  }

  onGenerateReports(): void {
    this.router.navigate(['/reports']);
  }

  onChurchSettings(): void {
    this.router.navigate(['/settings']);
  }

  // Utility methods
  formatNumber(value: number | undefined): string {
    return (value || 0).toLocaleString();
  }

  formatPercentage(value: number | undefined): string {
    return `${Math.round(value || 0)}%`;
  }

  getActionDescription(title: string): string {
    const descriptions: { [key: string]: string } = {
      'Manage Churches': 'View and manage all churches in the system',
      'System Users': 'Manage users across all churches',
      'System Reports': 'View comprehensive system analytics',
      'System Settings': 'Configure system-wide settings',
      'View Attendance': 'Check current attendance status',
      'Manage Members': 'Add and manage church members',
      'Generate Reports': 'Create attendance and member reports',
      'Church Settings': 'Configure church preferences'
    };
    return descriptions[title] || 'Quick access to this feature';
  }

  getActivityIcon(type: string): string {
    const icons: { [key: string]: string } = {
      'check-in': 'login',
      'new-member': 'person_add',
      'report': 'assessment',
      'setting': 'settings'
    };
    return icons[type] || 'info';
  }

  formatTime(timestamp: any): string {
    if (!timestamp) return '';
    // Handle different timestamp formats
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  }

  getGreeting(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  }

  /**
   * Calculate weekly attendance from live attendance data
   */
  private calculateWeeklyAttendance(liveAttendance: any): number {
    // For now, estimate weekly attendance as today's attendance * 7
    // In a real implementation, you'd query attendance records for the past week
    const todayAttendance = liveAttendance.totalCheckedIn || 0;

    // Simple estimation - in production, you'd want to query actual weekly data
    return todayAttendance * 7;
  }

  /**
   * Generate recent activity from members and attendance data
   */
  private generateRecentActivity(members: User[], liveAttendance: any): any[] {
    const activities: any[] = [];

    console.log('🔍 Generating recent activity:', {
      membersCount: members.length,
      liveAttendance
    });

    // Add recent check-ins from today's attendance
    if (liveAttendance && liveAttendance.recentCheckIns && liveAttendance.recentCheckIns.length > 0) {
      liveAttendance.recentCheckIns.slice(0, 5).forEach((checkIn: any) => {
        const member = members.find(m => m.id === checkIn.memberId);
        if (member && member.firstName && member.lastName) {
          activities.push({
            type: 'attendance',
            message: `${member.firstName} ${member.lastName} checked in`,
            timestamp: checkIn.checkInTime,
            user: {
              firstName: member.firstName,
              lastName: member.lastName,
              email: member.email,
              id: member.id
            }
          });
        } else {
          // Fallback if member not found
          activities.push({
            type: 'attendance',
            message: `Member checked in`,
            timestamp: checkIn.checkInTime,
            user: {
              firstName: 'Unknown',
              lastName: 'Member',
              email: '',
              id: checkIn.memberId || 'unknown'
            }
          });
        }
      });
    }

    // Add recent member registrations (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentMembers = members
      .filter(member => {
        if (!member.createdAt) return false;
        try {
          const createdDate = member.createdAt.toDate ? member.createdAt.toDate() : new Date(member.createdAt as any);
          return createdDate >= sevenDaysAgo && member.firstName && member.lastName;
        } catch (error) {
          console.warn('Error parsing member createdAt:', error);
          return false;
        }
      })
      .slice(0, 3);

    recentMembers.forEach(member => {
      activities.push({
        type: 'member',
        message: `${member.firstName} ${member.lastName} joined the church`,
        timestamp: member.createdAt,
        user: {
          firstName: member.firstName,
          lastName: member.lastName,
          email: member.email,
          id: member.id
        }
      });
    });

    // If no activities from attendance/members, create some sample activities
    if (activities.length === 0) {
      // Add sample activities based on existing members
      members.slice(0, 5).forEach((member, index) => {
        if (member.firstName && member.lastName) {
          activities.push({
            type: index % 2 === 0 ? 'attendance' : 'member',
            message: index % 2 === 0
              ? `${member.firstName} ${member.lastName} checked in`
              : `${member.firstName} ${member.lastName} joined the church`,
            timestamp: new Date(Date.now() - (index * 60 * 60 * 1000)), // Stagger by hours
            user: {
              firstName: member.firstName,
              lastName: member.lastName,
              email: member.email,
              id: member.id
            }
          });
        }
      });
    }

    // Sort by timestamp (most recent first)
    const sortedActivities = activities
      .sort((a, b) => {
        try {
          const timeA = a.timestamp.toDate ? a.timestamp.toDate() : new Date(a.timestamp);
          const timeB = b.timestamp.toDate ? b.timestamp.toDate() : new Date(b.timestamp);
          return timeB.getTime() - timeA.getTime();
        } catch (error) {
          console.warn('Error sorting activities:', error);
          return 0;
        }
      })
      .slice(0, 10); // Limit to 10 most recent activities

    console.log('✅ Generated activities:', sortedActivities);
    return sortedActivities;
  }

}
