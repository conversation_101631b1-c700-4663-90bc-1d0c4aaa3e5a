<div class="add-member-dialog">
  <div class="dialog-header">
    <h2 mat-dialog-title>
      <mat-icon>person_add</mat-icon>
      Add New Member
    </h2>
    <button mat-icon-button mat-dialog-close class="close-button">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <mat-dialog-content class="dialog-content">
    <form [formGroup]="memberForm" (ngSubmit)="onSubmit()" class="member-form">
      <!-- Personal Information Section -->
      <div class="form-section">
        <h3 class="section-title">Personal Information</h3>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>First Name</mat-label>
            <input matInput formControlName="firstName" placeholder="Enter first name">
            <mat-icon matSuffix>person</mat-icon>
            @if (hasFieldError('firstName')) {
            <mat-error>{{ getFieldError('firstName') }}</mat-error>
            }
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Last Name</mat-label>
            <input matInput formControlName="lastName" placeholder="Enter last name">
            <mat-icon matSuffix>person</mat-icon>
            @if (hasFieldError('lastName')) {
            <mat-error>{{ getFieldError('lastName') }}</mat-error>
            }
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Email</mat-label>
            <input matInput type="email" formControlName="email" placeholder="Enter email address">
            <mat-icon matSuffix>email</mat-icon>
            @if (hasFieldError('email')) {
            <mat-error>{{ getFieldError('email') }}</mat-error>
            }
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Phone Number</mat-label>
            <input matInput type="tel" formControlName="phone" placeholder="Enter phone number">
            <mat-icon matSuffix>phone</mat-icon>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Password</mat-label>
            <input matInput [type]="showPassword() ? 'text' : 'password'" formControlName="password"
              placeholder="Enter password (min 8 characters)">
            <button mat-icon-button matSuffix type="button" (click)="togglePasswordVisibility()"
              [attr.aria-label]="'Hide password'" [attr.aria-pressed]="showPassword()">
              <mat-icon>{{ showPassword() ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
            @if (hasFieldError('password')) {
            <mat-error>{{ getFieldError('password') }}</mat-error>
            }
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Confirm Password</mat-label>
            <input matInput [type]="showConfirmPassword() ? 'text' : 'password'" formControlName="confirmPassword"
              placeholder="Confirm password">
            <button mat-icon-button matSuffix type="button" (click)="toggleConfirmPasswordVisibility()"
              [attr.aria-label]="'Hide password'" [attr.aria-pressed]="showConfirmPassword()">
              <mat-icon>{{ showConfirmPassword() ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
            @if (hasFieldError('confirmPassword')) {
            <mat-error>{{ getFieldError('confirmPassword') }}</mat-error>
            }
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Date of Birth</mat-label>
            <input matInput type="date" formControlName="dateOfBirth" placeholder="Select date" class="date-input">
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Gender</mat-label>
            <mat-select formControlName="gender" placeholder="Select gender">
              @for (option of genderOptions; track option.value) {
              <mat-option [value]="option.value">{{ option.label }}</mat-option>
              }
            </mat-select>
            <mat-icon matSuffix>wc</mat-icon>
          </mat-form-field>
        </div>

        <mat-form-field appearance="outline" class="form-field full-width">
          <mat-label>Address</mat-label>
          <textarea matInput formControlName="address" placeholder="Enter home address" rows="2"></textarea>
          <mat-icon matSuffix>home</mat-icon>
        </mat-form-field>
      </div>

      <!-- Account Information Section -->
      <div class="form-section">
        <h3 class="section-title">Account Information</h3>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Password</mat-label>
            <input matInput [type]="showPassword() ? 'text' : 'password'" formControlName="password"
              placeholder="Enter password">
            <button mat-icon-button matSuffix type="button" (click)="togglePasswordVisibility()"
              [attr.aria-label]="'Toggle password visibility'">
              <mat-icon>{{ showPassword() ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
            @if (hasFieldError('password')) {
            <mat-error>{{ getFieldError('password') }}</mat-error>
            }
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Confirm Password</mat-label>
            <input matInput [type]="showConfirmPassword() ? 'text' : 'password'" formControlName="confirmPassword"
              placeholder="Confirm password">
            <button mat-icon-button matSuffix type="button" (click)="toggleConfirmPasswordVisibility()"
              [attr.aria-label]="'Toggle confirm password visibility'">
              <mat-icon>{{ showConfirmPassword() ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
            @if (hasFieldError('confirmPassword')) {
            <mat-error>{{ getFieldError('confirmPassword') }}</mat-error>
            }
          </mat-form-field>
        </div>

        <mat-form-field appearance="outline" class="form-field">
          <mat-label>Role</mat-label>
          <mat-select formControlName="role">
            @for (option of roleOptions; track option.value) {
            <mat-option [value]="option.value">{{ option.label }}</mat-option>
            }
          </mat-select>
          <mat-icon matSuffix>admin_panel_settings</mat-icon>
        </mat-form-field>
      </div>

      <!-- Emergency Contact Section -->
      <div class="form-section">
        <h3 class="section-title">Emergency Contact (Optional)</h3>

        <div class="form-row">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Emergency Contact Name</mat-label>
            <input matInput formControlName="emergencyContactName" placeholder="Enter contact name">
            <mat-icon matSuffix>contact_emergency</mat-icon>
          </mat-form-field>

          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Emergency Contact Phone</mat-label>
            <input matInput type="tel" formControlName="emergencyContactPhone" placeholder="Enter contact phone">
            <mat-icon matSuffix>phone</mat-icon>
          </mat-form-field>
        </div>
      </div>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions class="dialog-actions">
    <button mat-button type="button" (click)="onCancel()" [disabled]="isLoading()">
      Cancel
    </button>
    <button mat-raised-button color="primary" type="submit" (click)="onSubmit()"
      [disabled]="memberForm.invalid || isLoading()">
      @if (isLoading()) {
      <mat-spinner diameter="20"></mat-spinner>
      <span>Adding...</span>
      } @else {
      <ng-container>
        <mat-icon>person_add</mat-icon>
        <span>Add Member</span>
      </ng-container>
      }
    </button>
  </mat-dialog-actions>
</div>