import { Injectable, signal } from '@angular/core';
import { Observable, BehaviorSubject, from } from 'rxjs';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Messaging, getToken, onMessage, MessagePayload } from '@angular/fire/messaging';
import { Firestore, collection, doc, addDoc, updateDoc, query, where, getDocs, Timestamp } from '@angular/fire/firestore';
import { environment } from '@environments/environment';
import { firstValueFrom } from 'rxjs';

export interface FCMToken {
  id?: string;
  token: string;
  userId: string;
  churchId: string;
  deviceType: 'web' | 'android' | 'ios';
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface NotificationPayload {
  title: string;
  body: string;
  icon?: string;
  image?: string;
  data?: Record<string, any>;
}

export interface SendNotificationRequest {
  title: string;
  body: string;
  churchId: string;
  targetUserIds?: string[];
  targetTokens?: string[];
  data?: Record<string, any>;
  icon?: string;
  image?: string;
}

export interface NotificationResult {
  success: boolean;
  successCount: number;
  failureCount: number;
  errors: string[];
  messageId?: string;
}

@Injectable({
  providedIn: 'root'
})
export class FCMService {
  // FCM HTTP API endpoint
  private fcmEndpoint = 'https://fcm.googleapis.com/fcm/send';

  // Reactive state
  isLoading = signal<boolean>(false);
  error = signal<string | null>(null);
  currentToken = signal<string | null>(null);

  private messageSubject = new BehaviorSubject<MessagePayload | null>(null);
  public message$ = this.messageSubject.asObservable();

  constructor(
    private messaging: Messaging,
    private firestore: Firestore,
    private http: HttpClient
  ) {
    this.initializeMessaging();
  }

  /**
   * Initialize Firebase Cloud Messaging
   */
  private initializeMessaging(): void {
    // Listen for foreground messages
    onMessage(this.messaging, (payload) => {
      console.log('Message received in foreground:', payload);
      this.messageSubject.next(payload);
      this.showNotification(payload);
    });
  }

  /**
   * Request notification permission and get FCM token
   */
  async requestPermission(userId: string, churchId: string): Promise<string | null> {
    try {
      this.isLoading.set(true);
      this.error.set(null);

      // Request permission
      const permission = await Notification.requestPermission();
      if (permission !== 'granted') {
        throw new Error('Notification permission denied');
      }

      // Get FCM token
      const token = await getToken(this.messaging, {
        vapidKey: environment.fcm.vapidKey
      });

      if (token) {
        this.currentToken.set(token);
        await this.saveTokenToFirestore(token, userId, churchId);
        console.log('FCM token obtained and saved:', token);
        return token;
      } else {
        throw new Error('No registration token available');
      }
    } catch (error: any) {
      console.error('Error getting FCM token:', error);
      this.error.set(error.message);
      return null;
    } finally {
      this.isLoading.set(false);
    }
  }

  /**
   * Save FCM token to Firestore
   */
  private async saveTokenToFirestore(token: string, userId: string, churchId: string): Promise<void> {
    const tokensCollection = collection(this.firestore, 'fcmTokens');

    // Check if token already exists
    const existingTokenQuery = query(
      tokensCollection,
      where('token', '==', token),
      where('userId', '==', userId)
    );

    const existingTokens = await getDocs(existingTokenQuery);

    if (existingTokens.empty) {
      // Create new token document
      const tokenData: Omit<FCMToken, 'id'> = {
        token,
        userId,
        churchId,
        deviceType: 'web',
        isActive: true,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      await addDoc(tokensCollection, tokenData);
    } else {
      // Update existing token
      const existingDoc = existingTokens.docs[0];
      await updateDoc(existingDoc.ref, {
        isActive: true,
        updatedAt: Timestamp.now()
      });
    }
  }

  /**
   * Get all active FCM token objects for a church (for detailed info)
   */
  async getChurchTokenObjects(churchId: string): Promise<FCMToken[]> {
    console.log('🔍 Getting FCM tokens for church:', churchId);

    const tokensCollection = collection(this.firestore, 'fcmTokens');
    const tokensQuery = query(
      tokensCollection,
      where('churchId', '==', churchId),
      where('isActive', '==', true)
    );

    const querySnapshot = await getDocs(tokensQuery);
    const tokens: FCMToken[] = [];

    querySnapshot.forEach(doc => {
      const tokenData = doc.data() as Omit<FCMToken, 'id'>;
      tokens.push({
        id: doc.id,
        ...tokenData
      });
      console.log('📱 Found FCM token:', {
        id: doc.id,
        userId: tokenData.userId,
        deviceType: tokenData.deviceType,
        isActive: tokenData.isActive
      });
    });

    console.log(`✅ Total FCM tokens found for church ${churchId}: ${tokens.length}`);
    return tokens;
  }

  /**
   * Get FCM tokens for specific users
   */
  async getUserTokens(userIds: string[]): Promise<FCMToken[]> {
    if (userIds.length === 0) return [];

    const tokensCollection = collection(this.firestore, 'fcmTokens');
    const tokensQuery = query(
      tokensCollection,
      where('userId', 'in', userIds),
      where('isActive', '==', true)
    );

    const querySnapshot = await getDocs(tokensQuery);
    const tokens: FCMToken[] = [];

    querySnapshot.forEach(doc => {
      tokens.push({
        id: doc.id,
        ...doc.data() as Omit<FCMToken, 'id'>
      });
    });

    return tokens;
  }

  /**
   * Get all active FCM tokens for a specific church
   */
  async getChurchTokens(churchId: string): Promise<string[]> {
    try {
      console.log(`🔍 Fetching FCM tokens for church: ${churchId}`);

      const tokensCollection = collection(this.firestore, 'fcmTokens');
      const tokensQuery = query(
        tokensCollection,
        where('churchId', '==', churchId),
        where('isActive', '==', true)
      );

      const snapshot = await getDocs(tokensQuery);
      const tokens = snapshot.docs.map(doc => doc.data()['token'] as string);

      console.log(`✅ Found ${tokens.length} active tokens for church ${churchId}`);
      return tokens;
    } catch (error) {
      console.error('❌ Error fetching FCM tokens:', error);
      return [];
    }
  }

  /**
   * Send notification to multiple tokens using direct FCM HTTP API
   */
  async sendNotificationToTokens(tokens: string[], payload: NotificationPayload): Promise<any> {
    if (tokens.length === 0) {
      throw new Error('No tokens provided');
    }

    if (!environment.fcm.serverKey || environment.fcm.serverKey === 'YOUR_LEGACY_SERVER_KEY_FROM_FIREBASE_CONSOLE') {
      throw new Error('FCM Server Key not configured. Please add your server key to environment.fcm.serverKey');
    }

    console.log(`🚀 Sending notification to ${tokens.length} devices`);
    console.log('📱 Payload:', payload);

    const headers = new HttpHeaders({
      'Authorization': `key=${environment.fcm.serverKey}`,
      'Content-Type': 'application/json'
    });

    // Send to multiple tokens (max 1000 per request)
    const promises = tokens.map(token => {
      const fcmPayload = {
        to: token,
        notification: {
          title: payload.title,
          body: payload.body,
          image: payload.image,
          icon: payload.icon || '/assets/icons/church-icon.png',
          click_action: 'FLUTTER_NOTIFICATION_CLICK'
        },
        data: payload.data || {},
        priority: 'high'
      };

      return firstValueFrom(
        this.http.post(this.fcmEndpoint, fcmPayload, { headers })
      ).catch(error => {
        console.error('❌ Failed to send to token:', token, error);
        return { success: false, error, token };
      });
    });

    try {
      const results = await Promise.allSettled(promises);

      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      console.log(`✅ Notification results: ${successful} successful, ${failed} failed`);

      // Clean up invalid tokens
      await this.cleanupInvalidTokens(results, tokens);

      return {
        success: true,
        successCount: successful,
        failureCount: failed,
        totalSent: tokens.length
      };

    } catch (error) {
      console.error('❌ Error sending notifications:', error);
      throw error;
    }
  }

  /**
   * Send notification to entire church using direct FCM
   */
  async sendNotificationToChurch(churchId: string, payload: NotificationPayload): Promise<any> {
    try {
      const tokens = await this.getChurchTokens(churchId);

      if (tokens.length === 0) {
        throw new Error('No active devices found for this church');
      }

      return await this.sendNotificationToTokens(tokens, payload);

    } catch (error) {
      console.error('❌ Error sending church notification:', error);
      throw error;
    }
  }

  /**
   * Get count of active tokens for a church (for UI display)
   */
  async getChurchTokenCount(churchId: string): Promise<number> {
    try {
      const tokens = await this.getChurchTokens(churchId);
      return tokens.length;
    } catch (error) {
      console.error('❌ Error getting token count:', error);
      return 0;
    }
  }

  /**
   * Clean up invalid/expired tokens
   */
  private async cleanupInvalidTokens(results: any[], tokens: string[]): Promise<void> {
    try {
      const invalidTokens: string[] = [];

      results.forEach((result, index) => {
        if (result.status === 'rejected' ||
            (result.value && result.value.error)) {
          invalidTokens.push(tokens[index]);
        }
      });

      if (invalidTokens.length > 0) {
        console.log(`🧹 Cleaning up ${invalidTokens.length} invalid tokens`);

        // Remove invalid tokens from Firestore
        for (const token of invalidTokens) {
          const tokensCollection = collection(this.firestore, 'fcmTokens');
          const tokenQuery = query(
            tokensCollection,
            where('token', '==', token)
          );

          const snapshot = await getDocs(tokenQuery);
          snapshot.docs.forEach(async (docSnapshot) => {
            await updateDoc(doc(this.firestore, 'fcmTokens', docSnapshot.id), {
              isActive: false,
              updatedAt: Timestamp.now()
            });
          });
        }

        console.log(`✅ Marked ${invalidTokens.length} invalid tokens as inactive`);
      }
    } catch (error) {
      console.error('❌ Error cleaning up tokens:', error);
    }
  }

  /**
   * Send notification using direct FCM HTTP API (replaces Cloud Functions approach)
   */
  async sendNotification(request: SendNotificationRequest): Promise<NotificationResult> {
    try {
      this.isLoading.set(true);
      this.error.set(null);

      // Get target tokens
      let targetTokens: string[] = [];

      if (request.targetTokens) {
        targetTokens = request.targetTokens;
      } else if (request.targetUserIds) {
        const userTokens = await this.getUserTokens(request.targetUserIds);
        targetTokens = userTokens.map(t => t.token);
      } else {
        // Send to all church members
        targetTokens = await this.getChurchTokens(request.churchId);
      }

      if (targetTokens.length === 0) {
        console.warn('⚠️ No FCM tokens found for notification');
        return {
          success: false,
          successCount: 0,
          failureCount: 0,
          errors: ['No valid tokens found for notification']
        };
      }

      console.log(`📤 Sending notification to ${targetTokens.length} tokens`);

      // Create notification payload
      const payload: NotificationPayload = {
        title: request.title,
        body: request.body,
        icon: request.icon,
        image: request.image,
        data: request.data || {}
      };

      // Send notification directly using FCM HTTP API
      const fcmResult = await this.sendNotificationToTokens(targetTokens, payload);

      // Save notification to Firestore for history
      const notificationData = {
        title: request.title,
        body: request.body,
        churchId: request.churchId,
        targetTokens,
        data: request.data || {},
        icon: request.icon,
        image: request.image,
        status: fcmResult.success ? 'sent' : 'failed',
        result: fcmResult,
        createdAt: Timestamp.now(),
        sentAt: Timestamp.now()
      };

      const notificationsCollection = collection(this.firestore, 'notifications');
      const notificationDoc = await addDoc(notificationsCollection, notificationData);

      console.log('✅ Notification sent directly via FCM HTTP API');

      // Return actual results from FCM
      const result: NotificationResult = {
        success: fcmResult.success,
        successCount: fcmResult.successCount,
        failureCount: fcmResult.failureCount,
        errors: fcmResult.errors || [],
        messageId: notificationDoc.id
      };

      return result;
    } catch (error: any) {
      console.error('Error sending notification:', error);
      this.error.set(error.message);
      return {
        success: false,
        successCount: 0,
        failureCount: 1,
        errors: [error.message]
      };
    } finally {
      this.isLoading.set(false);
    }
  }

  /**
   * Show browser notification
   */
  private showNotification(payload: MessagePayload): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      const notification = new Notification(payload.notification?.title || 'New Message', {
        body: payload.notification?.body,
        icon: payload.notification?.icon || '/favicon.ico',
        data: payload.data
      });

      notification.onclick = () => {
        window.focus();
        notification.close();
        // Handle notification click
        if (payload.data && payload.data['url']) {
          window.location.href = payload.data['url'];
        }
      };
    }
  }

  /**
   * Deactivate FCM token
   */
  async deactivateToken(token: string): Promise<void> {
    const tokensCollection = collection(this.firestore, 'fcmTokens');
    const tokenQuery = query(tokensCollection, where('token', '==', token));

    const querySnapshot = await getDocs(tokenQuery);
    querySnapshot.forEach(async (doc) => {
      await updateDoc(doc.ref, {
        isActive: false,
        updatedAt: Timestamp.now()
      });
    });
  }

  /**
   * Clear current token
   */
  clearToken(): void {
    this.currentToken.set(null);
  }
}
