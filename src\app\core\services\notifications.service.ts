import { Injectable, signal, inject } from '@angular/core';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Firestore, collection, doc, addDoc, query, orderBy, getDocs, Timestamp, where, limit } from '@angular/fire/firestore';
import { Auth } from '@angular/fire/auth';
import { FCMService, NotificationResult as FCMNotificationResult } from './fcm.service';

export interface SendNotificationRequest {
  title: string;
  message: string;
  type: NotificationType;
  priority?: NotificationPriority;
  data?: Record<string, any>;
  targetMemberId?: string;
  scheduledAt?: Date;
}

export interface SendServiceScheduleNotificationRequest {
  title: string;
  message: string;
  serviceData?: Record<string, any>;
}

export interface NotificationResult {
  notificationId: string;
  totalRecipients: number;
  successCount: number;
  failureCount: number;
  errors: string[];
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  status: NotificationStatus;
  priority: NotificationPriority;
  recipientCount: number;
  deliveredCount: number;
  failedCount: number;
  createdAt: Date;
  sentAt?: Date;
  targetMemberId?: string;
  data?: Record<string, any>;
  result?: NotificationResult;
  sender?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export enum NotificationType {
  SERVICE_SCHEDULE = 'service_schedule',
  ATTENDANCE_REMINDER = 'attendance_reminder',
  CHURCH_ANNOUNCEMENT = 'church_announcement',
  SYSTEM_NOTIFICATION = 'system_notification',
}

export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  FAILED = 'failed',
}

export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
}

export interface NotificationHistory {
  notifications: Notification[];
  total: number;
  limit: number;
  offset: number;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationsService {
  // Firebase-based service - no API URL needed

  // Reactive state
  isLoading = signal<boolean>(false);
  error = signal<string | null>(null);

  private notificationsSubject = new BehaviorSubject<Notification[]>([]);
  notifications$ = this.notificationsSubject.asObservable();

  constructor(
    private firestore: Firestore,
    private fcmService: FCMService,
    private auth: Auth
  ) { }

  /**
   * Get current user's church ID from auth context
   */
  private getCurrentChurchId(): string {
    // For testing purposes, let's use a church ID that matches the current user
    // In production, this should get the church ID from the authenticated user's profile
    if (this.auth.currentUser) {
      // For now, we'll derive a church ID from the user's email domain or use a default
      const email = this.auth.currentUser.email;
      if (email?.includes('testchurch.com')) {
        return 'testchurch-001';
      }
    }
    return 'default-church-id';
  }

  /**
   * Send a general notification to church members
   */
  sendNotification(request: SendNotificationRequest): Observable<{ message: string; result: NotificationResult }> {
    this.isLoading.set(true);
    this.error.set(null);

    const churchId = this.getCurrentChurchId();

    const fcmRequest = {
      title: request.title,
      body: request.message,
      churchId: churchId,
      targetUserIds: request.targetMemberId ? [request.targetMemberId] : undefined,
      data: request.data,
      icon: '/assets/icons/notification-icon.png',
      image: undefined
    };

    return new Observable(observer => {
      this.fcmService.sendNotification(fcmRequest).then(async fcmResult => {
        const result: NotificationResult = {
          notificationId: fcmResult.messageId || 'unknown',
          totalRecipients: fcmResult.successCount + fcmResult.failureCount,
          successCount: fcmResult.successCount,
          failureCount: fcmResult.failureCount,
          errors: fcmResult.errors
        };

        // Save notification to history
        try {
          await this.saveNotificationToHistory(request, result, churchId);
          console.log('✅ Notification saved to history');
        } catch (error) {
          console.error('❌ Error saving notification to history:', error);
          // Don't fail the whole operation if history save fails
        }

        this.isLoading.set(false);
        observer.next({
          message: `Notification sent to ${result.totalRecipients} members`,
          result
        });
        observer.complete();
      }).catch(error => {
        this.isLoading.set(false);
        this.error.set(error.message);
        observer.error(error);
      });
    });
  }

  /**
   * Send service schedule notification
   */
  sendServiceScheduleNotification(request: SendServiceScheduleNotificationRequest): Observable<{ message: string; result: NotificationResult }> {
    this.isLoading.set(true);
    this.error.set(null);

    // TODO: Implement Firebase-based service schedule notification
    const mockResult: NotificationResult = {
      notificationId: 'mock-id',
      totalRecipients: 0,
      successCount: 0,
      failureCount: 0,
      errors: []
    };

    this.isLoading.set(false);
    return of({ message: 'Service schedule notification sent successfully', result: mockResult });
  }

  /**
   * Get notification history for the church
   */
  getNotifications(limitCount: number = 50, offset: number = 0): Observable<NotificationHistory> {
    this.isLoading.set(true);
    this.error.set(null);

    const churchId = this.getCurrentChurchId();
    const notificationsCollection = collection(this.firestore, 'notifications');
    const notificationsQuery = query(
      notificationsCollection,
      where('churchId', '==', churchId),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    return new Observable(observer => {
      getDocs(notificationsQuery).then(querySnapshot => {
        const notifications: Notification[] = [];
        querySnapshot.forEach(doc => {
          const data = doc.data();
          notifications.push({
            id: doc.id,
            title: data['title'],
            message: data['body'],
            type: data['type'] || NotificationType.CHURCH_ANNOUNCEMENT,
            priority: data['priority'] || NotificationPriority.NORMAL,
            status: data['status'] as NotificationStatus,
            createdAt: data['createdAt'].toDate(),
            sentAt: data['sentAt']?.toDate(),
            targetMemberId: data['targetMemberId'],
            data: data['data'] || {},
            result: data['result'],
            recipientCount: data['result']?.totalRecipients || 0,
            deliveredCount: data['result']?.successCount || 0,
            failedCount: data['result']?.failureCount || 0
          });
        });

        const history: NotificationHistory = {
          notifications,
          total: notifications.length,
          limit: limitCount,
          offset
        };

        this.notificationsSubject.next(notifications);
        this.isLoading.set(false);
        observer.next(history);
        observer.complete();
      }).catch(error => {
        this.isLoading.set(false);
        this.error.set(error.message);
        observer.error(error);
      });
    });
  }

  /**
   * Get notification by ID
   */
  getNotificationById(id: string): Observable<Notification> {
    // TODO: Implement Firebase-based notification retrieval by ID
    return of({} as Notification);
  }

  /**
   * Load notifications and update the subject
   */
  private loadNotifications(): void {
    this.getNotifications().subscribe({
      next: (response) => {
        this.notificationsSubject.next(response.notifications);
      },
      error: (error) => {
        console.error('Error loading notifications:', error);
      }
    });
  }

  /**
   * Helper method to send notification when service times are updated
   */
  sendServiceTimesUpdateNotification(serviceTimes: any[]): Observable<{ message: string; result: NotificationResult }> {
    const serviceNames = serviceTimes.map(st => st.name).join(', ');

    return this.sendServiceScheduleNotification({
      title: 'Service Schedule Updated',
      message: `Service times have been updated for: ${serviceNames}. Please check the latest schedule.`,
      serviceData: {
        serviceTimes: serviceTimes,
        updateType: 'service_times_update'
      }
    });
  }

  /**
   * Helper method to send notification for new service announcement
   */
  sendServiceAnnouncementNotification(
    title: string,
    message: string,
    serviceData?: any
  ): Observable<{ message: string; result: NotificationResult }> {
    return this.sendServiceScheduleNotification({
      title,
      message,
      serviceData: {
        ...serviceData,
        updateType: 'service_announcement'
      }
    });
  }

  /**
   * Helper method to send general church announcement
   */
  sendChurchAnnouncementNotification(
    title: string,
    message: string,
    priority: NotificationPriority = NotificationPriority.NORMAL
  ): Observable<{ message: string; result: NotificationResult }> {
    return this.sendNotification({
      title,
      message,
      type: NotificationType.CHURCH_ANNOUNCEMENT,
      priority,
      data: {
        updateType: 'church_announcement'
      }
    });
  }

  /**
   * Initialize FCM for current user
   */
  async initializeFCM(userId: string, churchId: string): Promise<boolean> {
    try {
      const token = await this.fcmService.requestPermission(userId, churchId);
      return token !== null;
    } catch (error) {
      console.error('Failed to initialize FCM:', error);
      return false;
    }
  }

  /**
   * Get FCM service instance for direct access
   */
  getFCMService(): FCMService {
    return this.fcmService;
  }

  /**
   * Clear error state
   */
  clearError(): void {
    this.error.set(null);
  }

  /**
   * Get current notifications
   */
  getCurrentNotifications(): Notification[] {
    return this.notificationsSubject.value;
  }

  /**
   * Save notification to history collection
   */
  private async saveNotificationToHistory(
    request: SendNotificationRequest,
    result: NotificationResult,
    churchId: string
  ): Promise<void> {
    const notificationsCollection = collection(this.firestore, 'notifications');

    const notificationDoc = {
      title: request.title,
      body: request.message,
      type: request.type || NotificationType.CHURCH_ANNOUNCEMENT,
      priority: request.priority || NotificationPriority.NORMAL,
      status: NotificationStatus.SENT,
      churchId: churchId,
      createdAt: Timestamp.now(),
      sentAt: Timestamp.now(),
      targetMemberId: request.targetMemberId || null,
      data: request.data || {},
      result: {
        notificationId: result.notificationId,
        totalRecipients: result.totalRecipients,
        successCount: result.successCount,
        failureCount: result.failureCount,
        errors: result.errors || []
      }
    };

    await addDoc(notificationsCollection, notificationDoc);
  }
}
