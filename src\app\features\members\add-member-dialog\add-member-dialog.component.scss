.add-member-dialog {
  width: 100%;
  max-width: 600px;
  
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    border-bottom: 1px solid #e0e0e0;
    
    h2 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      font-size: 1.5rem;
      font-weight: 500;
      color: #1976d2;
      
      mat-icon {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
      }
    }
    
    .close-button {
      color: #666;
      
      &:hover {
        color: #333;
        background-color: #f5f5f5;
      }
    }
  }
  
  .dialog-content {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
    
    .member-form {
      .form-section {
        margin-bottom: 32px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .section-title {
          font-size: 1.1rem;
          font-weight: 500;
          color: #333;
          margin: 0 0 16px 0;
          padding-bottom: 8px;
          border-bottom: 2px solid #e3f2fd;
        }
        
        .form-row {
          display: flex;
          gap: 16px;
          margin-bottom: 16px;
          
          .form-field {
            flex: 1;
          }
        }
        
        .form-field {
          width: 100%;
          
          &.full-width {
            width: 100%;
          }
          
          mat-icon {
            color: #666;
          }
          
          &.mat-form-field-invalid {
            .mat-form-field-outline-thick {
              color: #f44336;
            }
          }
        }
      }
    }
  }
  
  .dialog-actions {
    padding: 16px 24px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    
    button {
      min-width: 100px;
      
      &[mat-raised-button] {
        display: flex;
        align-items: center;
        gap: 8px;
        
        mat-spinner {
          margin-right: 8px;
        }
        
        mat-icon {
          font-size: 1.2rem;
          width: 1.2rem;
          height: 1.2rem;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .add-member-dialog {
    max-width: 95vw;
    
    .dialog-content {
      padding: 16px;
      
      .member-form {
        .form-section {
          .form-row {
            flex-direction: column;
            gap: 8px;
          }
        }
      }
    }
    
    .dialog-actions {
      padding: 12px 16px;
      flex-direction: column-reverse;
      
      button {
        width: 100%;
      }
    }
  }
}

// Style for HTML date input to match Material Design
.date-input {
  cursor: pointer;

  &::-webkit-calendar-picker-indicator {
    cursor: pointer;
    filter: invert(0.5);

    &:hover {
      filter: invert(0.3);
    }
  }

  // Remove default styling on focus
  &:focus {
    outline: none;
  }
}

// Custom snackbar styles
::ng-deep {
  .success-snackbar {
    background-color: #4caf50 !important;
    color: white !important;
  }

  .error-snackbar {
    background-color: #f44336 !important;
    color: white !important;
  }
}
