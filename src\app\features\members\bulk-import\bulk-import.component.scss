.bulk-import-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;

  .header-content {
    flex: 1;

    h1 {
      margin: 0 0 0.5rem 0;
      font-size: 2rem;
      font-weight: 700;
      color: #1a202c;
    }

    p {
      margin: 0;
      color: #718096;
      font-size: 1rem;
      line-height: 1.5;
    }
  }

  .header-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;

    @media (max-width: 768px) {
      width: 100%;
      justify-content: stretch;

      button {
        flex: 1;
      }
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }
}

// Upload Card Styles
.upload-card {
  margin-bottom: 2rem;

  .drop-zone {
    border: 2px dashed #cbd5e0;
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f7fafc;

    &:hover, &.drag-over {
      border-color: #4299e1;
      background: #ebf8ff;
      transform: translateY(-2px);
    }

    .drop-zone-content {
      .upload-icon {
        font-size: 4rem;
        color: #4299e1;
        margin-bottom: 1rem;
      }

      h3 {
        margin: 0 0 0.5rem 0;
        color: #2d3748;
        font-weight: 600;
      }

      p {
        margin: 0 0 2rem 0;
        color: #718096;
      }

      .file-requirements {
        text-align: left;
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        max-width: 400px;
        margin: 0 auto;

        p {
          margin: 0 0 0.5rem 0;
          font-weight: 600;
          color: #2d3748;
        }

        ul {
          margin: 0;
          padding-left: 1.5rem;
          color: #4a5568;

          li {
            margin-bottom: 0.25rem;
          }
        }
      }
    }
  }
}

// Progress Card Styles
.progress-card {
  margin-bottom: 2rem;

  .progress-content {
    text-align: center;
    padding: 2rem;

    mat-icon {
      font-size: 3rem;
      color: #4299e1;
      margin-bottom: 1rem;

      &.spinning {
        animation: spin 2s linear infinite;
      }
    }

    h3 {
      margin: 0 0 0.5rem 0;
      color: #2d3748;
    }

    p {
      margin: 0 0 1rem 0;
      color: #718096;

      &.current-item {
        font-weight: 500;
        color: #4299e1;
      }
    }

    mat-progress-bar {
      margin: 1rem 0;
    }

    .progress-stats {
      display: flex;
      justify-content: center;
      gap: 2rem;
      margin-top: 1rem;

      .success-count {
        color: #38a169;
        font-weight: 500;
      }

      .failure-count {
        color: #e53e3e;
        font-weight: 500;
      }
    }
  }
}

// Preview Section Styles
.preview-section {
  .summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;

    .summary-card {
      &.total .summary-content mat-icon { color: #4299e1; }
      &.valid .summary-content mat-icon { color: #38a169; }
      &.invalid .summary-content mat-icon { color: #e53e3e; }

      .summary-content {
        display: flex;
        align-items: center;
        gap: 1rem;

        mat-icon {
          font-size: 2rem;
        }

        .summary-text {
          h3 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
          }

          p {
            margin: 0;
            color: #718096;
            font-size: 0.875rem;
          }
        }
      }
    }
  }

  .preview-table-card {
    .table-container {
      max-height: 500px;
      overflow-y: auto;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
    }

    .preview-table {
      width: 100%;

      .invalid-row {
        background-color: #fed7d7;
        color: #c53030;
      }

      th {
        background-color: #f7fafc;
        font-weight: 600;
        color: #2d3748;
      }

      td, th {
        padding: 0.75rem;
        border-bottom: 1px solid #e2e8f0;
      }

      mat-chip-set {
        mat-chip {
          font-size: 0.75rem;
          margin: 0.125rem;
        }
      }
    }
  }
}

// Results Card Styles
.results-card {
  margin-bottom: 2rem;

  mat-card-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    mat-icon {
      font-size: 1.5rem;
    }
  }

  .results-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;

    .result-stat {
      text-align: center;
      padding: 1rem;
      border-radius: 8px;
      background: #f7fafc;

      &.success {
        background: #f0fff4;
        border: 1px solid #9ae6b4;
      }

      &.failure {
        background: #fed7d7;
        border: 1px solid #feb2b2;
      }

      h3 {
        margin: 0 0 0.5rem 0;
        font-size: 2rem;
        font-weight: 700;
        color: #2d3748;
      }

      p {
        margin: 0;
        color: #718096;
        font-size: 0.875rem;
      }
    }
  }

  .error-list, .duplicate-list {
    margin-bottom: 1rem;
    padding: 1rem;
    background: #fed7d7;
    border-radius: 8px;
    border: 1px solid #feb2b2;

    h4 {
      margin: 0 0 0.5rem 0;
      color: #c53030;
      font-size: 1rem;
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;
      color: #c53030;

      li {
        margin-bottom: 0.25rem;
        font-size: 0.875rem;
      }
    }
  }

  .duplicate-list {
    background: #fef5e7;
    border-color: #f6ad55;

    h4, ul {
      color: #c05621;
    }
  }
}

// Animations
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Responsive Design
@media (max-width: 768px) {
  .bulk-import-container {
    padding: 0.5rem;
  }

  .drop-zone {
    padding: 2rem 1rem !important;

    .drop-zone-content {
      .upload-icon {
        font-size: 3rem !important;
      }

      .file-requirements {
        max-width: none !important;
      }
    }
  }

  .summary-cards {
    grid-template-columns: 1fr !important;
  }

  .results-summary {
    grid-template-columns: 1fr !important;
  }

  .preview-table {
    font-size: 0.875rem;

    td, th {
      padding: 0.5rem !important;
    }
  }

  .table-container {
    max-height: 400px !important;
  }
}

// Material Design Overrides
mat-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

mat-card-actions {
  display: flex;
  gap: 1rem;
  padding: 1rem 1.5rem;

  @media (max-width: 768px) {
    flex-direction: column;

    button {
      width: 100%;
    }
  }
}
