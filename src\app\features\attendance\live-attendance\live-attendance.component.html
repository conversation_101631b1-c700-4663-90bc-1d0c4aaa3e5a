<div class="live-attendance-container">
  <!-- Header Section -->
  <div class="page-header">
    <div class="header-content">
      <h1>Live Attendance</h1>
      <p>Real-time attendance monitoring for today's service</p>

      <!-- Real-time Status -->
      <div class="realtime-status">
        <div class="status-indicator">
          <div class="status-dot" [class.active]="autoRefresh()"></div>
          <span>{{ autoRefresh() ? 'Live' : 'Paused' }}</span>
        </div>
        <div class="last-updated">
          Last updated: {{ getLastUpdatedText() }}
        </div>
      </div>

      <!-- Geofence Status -->
      @if (geofenceStatus()) {
      <div class="geofence-status" [class.inside]="geofenceStatus()!.isInside">
        <div class="geofence-indicator">
          <div class="geofence-dot" [class.inside]="geofenceStatus()!.isInside"></div>
          <span>{{ geofenceStatus()!.isInside ? '✅ Inside church area' : '📍 Outside church area' }}</span>
        </div>
        <div class="geofence-details">
          Distance: {{ formatDistance(geofenceStatus()!.distance) }} |
          Accuracy: ±{{ geofenceStatus()!.accuracy }}m
        </div>

      </div>
      }

      @if (error()) {
      <div class="error-message">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z" />
        </svg>
        {{ error() }}
        <button (click)="onRefresh()" class="retry-btn">Retry</button>
      </div>
      }
    </div>

    <div class="header-actions">
      <button class="action-btn secondary" (click)="onToggleAutoRefresh()">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          @if (autoRefresh()) {
          <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" />
          } @else {
          <path d="M8 5v14l11-7z" />
          }
        </svg>
        {{ autoRefresh() ? 'Pause' : 'Resume' }}
      </button>
      <button class="action-btn secondary" (click)="onRefresh()" [disabled]="isLoading()">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path
            d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z" />
        </svg>
        Refresh
      </button>
    </div>
  </div>

  <!-- Statistics Cards -->
  @if (attendanceStats()) {
  <div class="stats-grid">
    <div class="stat-card present">
      <div class="stat-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
        </svg>
      </div>
      <div class="stat-content">
        <h3>Present</h3>
        <div class="stat-value">{{ attendanceStats()!.totalPresent }}</div>
        <div class="stat-label">Members checked in</div>
      </div>
    </div>

    <div class="stat-card late">
      <div class="stat-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path
            d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z" />
        </svg>
      </div>
      <div class="stat-content">
        <h3>Late</h3>
        <div class="stat-value">{{ attendanceStats()!.totalLate }}</div>
        <div class="stat-label">Late arrivals</div>
      </div>
    </div>

    <div class="stat-card absent">
      <div class="stat-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path
            d="M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z" />
        </svg>
      </div>
      <div class="stat-content">
        <h3>Absent</h3>
        <div class="stat-value">{{ attendanceStats()!.absentCount }}</div>
        <div class="stat-label">Not checked in</div>
      </div>
    </div>

    <div class="stat-card total">
      <div class="stat-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path
            d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A3.01 3.01 0 0 0 17.1 7H16c-.8 0-1.54.37-2.03.97L12 10.5 10.03 7.97A2.99 2.99 0 0 0 8 7H6.9c-1.3 0-2.4.84-2.86 2.37L1.5 16H4v6h4v-6h2v6h4z" />
        </svg>
      </div>
      <div class="stat-content">
        <h3>Attendance Rate</h3>
        <div class="stat-value">{{ attendanceStats()!.attendanceRate }}%</div>
        <div class="stat-label">{{ attendanceStats()!.totalCheckedIn }} of {{ attendanceStats()!.totalMembers }}</div>
      </div>
    </div>
  </div>
  }

  <!-- Filters and Controls -->
  <div class="controls-section">
    <div class="search-controls">
      <div class="search-box">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path
            d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" />
        </svg>
        <input type="text" placeholder="Search members..." [value]="searchQuery()"
          (input)="onSearchChange($any($event.target).value)" />
      </div>
    </div>

    <div class="filter-controls">
      <select [value]="selectedStatus()" (change)="onStatusFilterChange($any($event.target).value)">
        <option value="all">All Status</option>
        <option value="present">Present</option>
        <option value="late">Late</option>
        <option value="absent">Absent</option>
      </select>
    </div>
  </div>

  <!-- Attendance List -->
  <div class="attendance-list-container">
    @if (isLoading()) {
    <div class="loading-state">
      <div class="loading-spinner"></div>
      <p>Loading live attendance data...</p>
    </div>
    } @else if (attendanceRecords().length === 0) {
    <div class="empty-state">
      <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
        <path
          d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
      </svg>
      <h3>No attendance records found</h3>
      <p>No members have checked in yet today, or your search didn't match any records.</p>
    </div>
    } @else {
    <div class="attendance-table">
      <div class="table-header">
        <div class="header-cell sortable" (click)="onSortChange('name')">
          Member Name
          @if (sortBy() === 'name') {
          <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
            @if (sortOrder() === 'asc') {
            <path d="M7 14l5-5 5 5z" />
            } @else {
            <path d="M7 10l5 5 5-5z" />
            }
          </svg>
          }
        </div>
        <div class="header-cell">Status</div>
        <div class="header-cell sortable" (click)="onSortChange('checkInTime')">
          Check-in Time
          @if (sortBy() === 'checkInTime') {
          <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
            @if (sortOrder() === 'asc') {
            <path d="M7 14l5-5 5 5z" />
            } @else {
            <path d="M7 10l5 5 5-5z" />
            }
          </svg>
          }
        </div>
        <div class="header-cell sortable" (click)="onSortChange('distance')">
          Distance
          @if (sortBy() === 'distance') {
          <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
            @if (sortOrder() === 'asc') {
            <path d="M7 14l5-5 5 5z" />
            } @else {
            <path d="M7 10l5 5 5-5z" />
            }
          </svg>
          }
        </div>
        <div class="header-cell">Verification</div>
      </div>

      <div class="table-body">
        @for (record of attendanceRecords(); track record.id) {
        <div class="table-row">
          <div class="cell member-cell">
            <div class="member-avatar">
              @if (record.member?.profilePicture) {
              <img [src]="record.member?.profilePicture" [alt]="record.member?.fullName || 'Member'" />
              } @else {
              <div class="avatar-placeholder">
                {{ (record.member?.firstName || 'U')[0] }}{{ (record.member?.lastName || '')[0] }}
              </div>
              }
            </div>
            <div class="member-info">
              <div class="member-name">{{ record.member?.fullName || 'Unknown Member' }}</div>
              <div class="member-email">{{ record.member?.email || '' }}</div>
            </div>
          </div>

          <div class="cell status-cell">
            <div class="status-badge" [style.background-color]="getStatusColor(record.status)">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                <path [attr.d]="getStatusIcon(record.status)" />
              </svg>
              {{ record.status | titlecase }}
            </div>
          </div>

          <div class="cell time-cell">
            <div class="check-in-time">{{ record.checkInTimeFormatted }}</div>
            <div class="check-in-method">{{ record.method }}</div>
          </div>

          <div class="cell distance-cell">
            {{ formatDistance(record.distance) }}
          </div>

          <div class="cell verification-cell">
            @if (record.isVerified) {
            <span class="verification-badge verified">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
              </svg>
              Verified
            </span>
            } @else if (record.isFlagged) {
            <span class="verification-badge flagged">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z" />
              </svg>
              Flagged
            </span>
            } @else {
            <span class="verification-badge pending">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,17A1.5,1.5 0 0,1 10.5,15.5A1.5,1.5 0 0,1 12,14A1.5,1.5 0 0,1 13.5,15.5A1.5,1.5 0 0,1 12,17M12,10A1,1 0 0,1 13,11V13A1,1 0 0,1 12,14A1,1 0 0,1 11,13V11A1,1 0 0,1 12,10Z" />
              </svg>
              Pending
            </span>
            }
          </div>
        </div>
        }
      </div>
    </div>
    }
  </div>
</div>