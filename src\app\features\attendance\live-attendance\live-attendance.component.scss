.live-attendance-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;

  .header-content {
    flex: 1;

    h1 {
      margin: 0 0 0.5rem 0;
      font-size: 2rem;
      font-weight: 700;
      color: #1a202c;
    }

    p {
      margin: 0;
      color: #718096;
      font-size: 1rem;
    }

    .realtime-status {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-top: 0.75rem;

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #e2e8f0;
          transition: background-color 0.3s ease;

          &.active {
            background-color: #38a169;
            animation: pulse 2s infinite;
          }
        }

        span {
          font-size: 0.875rem;
          font-weight: 500;
          color: #4a5568;
        }
      }

      .last-updated {
        font-size: 0.75rem;
        color: #a0aec0;
      }
    }

    .geofence-status {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-top: 0.75rem;
      padding: 0.75rem 1rem;
      background: #f7fafc;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      transition: all 0.3s ease;

      &.inside {
        background: #f0fff4;
        border-color: #9ae6b4;
      }

      .geofence-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .geofence-dot {
          width: 10px;
          height: 10px;
          border-radius: 50%;
          background-color: #e2e8f0;
          transition: background-color 0.3s ease;

          &.inside {
            background-color: #38a169;
            animation: pulse 2s infinite;
          }
        }

        span {
          font-size: 0.875rem;
          font-weight: 500;
          color: #2d3748;
        }
      }

      .geofence-details {
        font-size: 0.75rem;
        color: #718096;
      }

      .manual-checkin-btn {
        margin-left: auto;
        padding: 0.5rem 1rem;
        background: #4299e1;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background: #3182ce;
        }

        &:active {
          background: #2c5aa0;
        }
      }
    }

    .error-message {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 1rem;
      background: #fed7d7;
      border: 1px solid #feb2b2;
      border-radius: 8px;
      color: #c53030;
      font-size: 0.875rem;
      margin-top: 1rem;

      svg {
        flex-shrink: 0;
      }

      .retry-btn {
        margin-left: auto;
        padding: 0.25rem 0.75rem;
        background: #c53030;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 0.75rem;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background: #9c2626;
        }
      }
    }
  }

  .header-actions {
    display: flex;
    gap: 0.75rem;
    flex-shrink: 0;
  }
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #4a5568;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: #f7fafc;
    border-color: #cbd5e0;
    color: #2d3748;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.secondary {
    background: #f7fafc;
  }

  svg {
    flex-shrink: 0;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 1rem;

  .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .stat-content {
    flex: 1;

    h3 {
      margin: 0 0 0.5rem 0;
      font-size: 0.875rem;
      font-weight: 500;
      color: #718096;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .stat-value {
      font-size: 2rem;
      font-weight: 700;
      color: #1a202c;
      line-height: 1;
      margin-bottom: 0.25rem;
    }

    .stat-label {
      font-size: 0.875rem;
      color: #718096;
    }
  }

  &.present .stat-icon {
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
  }

  &.late .stat-icon {
    background: linear-gradient(135deg, #d69e2e 0%, #b7791f 100%);
  }

  &.absent .stat-icon {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  }

  &.total .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
}

.controls-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;

  .search-controls {
    flex: 1;
    max-width: 400px;

    .search-box {
      position: relative;
      display: flex;
      align-items: center;

      svg {
        position: absolute;
        left: 0.75rem;
        color: #a0aec0;
        z-index: 1;
      }

      input {
        width: 100%;
        padding: 0.75rem 0.75rem 0.75rem 2.5rem;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        font-size: 0.875rem;
        background: #f7fafc;
        transition: all 0.2s ease;

        &:focus {
          outline: none;
          border-color: #667eea;
          background: white;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        &::placeholder {
          color: #a0aec0;
        }
      }
    }
  }

  .filter-controls {
    display: flex;
    gap: 0.75rem;

    select {
      padding: 0.75rem;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      background: white;
      color: #4a5568;
      font-size: 0.875rem;
      cursor: pointer;
      transition: border-color 0.2s ease;

      &:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }
  }
}

.attendance-list-container {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-state {
  color: #718096;

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
}

.empty-state {
  color: #a0aec0;

  svg {
    margin-bottom: 1.5rem;
    opacity: 0.5;
  }

  h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #4a5568;
  }
}

.loading-state p,
.empty-state p {
  margin: 0;
  font-size: 0.875rem;
}

.attendance-table {
  .table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1.5fr 1fr 1fr;
    gap: 1rem;
    padding: 1rem 1.5rem;
    background: #f7fafc;
    border-bottom: 1px solid #e2e8f0;

    .header-cell {
      font-size: 0.75rem;
      font-weight: 600;
      color: #4a5568;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      display: flex;
      align-items: center;
      gap: 0.25rem;

      &.sortable {
        cursor: pointer;
        user-select: none;
        transition: color 0.2s ease;

        &:hover {
          color: #2d3748;
        }

        svg {
          opacity: 0.6;
        }
      }
    }
  }

  .table-body {
    .table-row {
      display: grid;
      grid-template-columns: 2fr 1fr 1.5fr 1fr 1fr;
      gap: 1rem;
      padding: 1rem 1.5rem;
      border-bottom: 1px solid #f7fafc;
      transition: background-color 0.2s ease;

      &:hover {
        background: #f7fafc;
      }

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .cell {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
  }

  .member-cell {
    gap: 0.75rem;

    .member-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .avatar-placeholder {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.875rem;
      }
    }

    .member-info {
      flex: 1;
      min-width: 0;

      .member-name {
        font-weight: 500;
        color: #1a202c;
        margin-bottom: 0.125rem;
      }

      .member-email {
        font-size: 0.75rem;
        color: #718096;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .status-cell {
    .status-badge {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.25rem 0.5rem;
      border-radius: 6px;
      color: white;
      font-size: 0.75rem;
      font-weight: 500;
      text-transform: capitalize;
    }
  }

  .time-cell {
    flex-direction: column;
    align-items: flex-start;

    .check-in-time {
      font-weight: 500;
      color: #1a202c;
      margin-bottom: 0.125rem;
    }

    .check-in-method {
      font-size: 0.75rem;
      color: #718096;
      text-transform: capitalize;
    }
  }

  .distance-cell {
    color: #4a5568;
    font-weight: 500;
  }

  .verification-cell {
    .verification-badge {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.25rem 0.5rem;
      border-radius: 6px;
      font-size: 0.75rem;
      font-weight: 500;

      &.verified {
        background: #c6f6d5;
        color: #2f855a;
      }

      &.flagged {
        background: #fed7d7;
        color: #c53030;
      }

      &.pending {
        background: #e2e8f0;
        color: #718096;
      }
    }
  }
}

// Animations
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(56, 161, 105, 0.7);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(56, 161, 105, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(56, 161, 105, 0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@media (max-width: 1024px) {
  .page-header {
    flex-direction: column;
    gap: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .controls-section {
    flex-direction: column;
    gap: 1rem;
  }

  .attendance-table .table-header {
    display: none;
  }

  .attendance-table .table-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}