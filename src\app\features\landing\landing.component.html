<!-- Header -->
<header class="header">
  <mat-toolbar class="toolbar">
    <div class="toolbar-content">
      <div class="logo">
        <mat-icon class="logo-icon">church</mat-icon>
        <span class="logo-text">{{ appName }}</span>
      </div>
      <nav class="nav-links">
        <button mat-button (click)="scrollToFeatures()">Features</button>
        <button mat-button (click)="scrollToTestimonials()">Testimonials</button>
        <button mat-button routerLink="/auth/login" class="login-btn">Login</button>
        <button mat-raised-button routerLink="/auth/church-registration" class="signup-btn">Get Started</button>
      </nav>
      <button mat-icon-button class="mobile-menu" [matMenuTriggerFor]="menu">
        <mat-icon>menu</mat-icon>
      </button>
    </div>
  </mat-toolbar>
</header>

<!-- Mobile Menu -->
<mat-menu #menu="matMenu">
  <button mat-menu-item (click)="scrollToFeatures()">Features</button>
  <button mat-menu-item (click)="scrollToTestimonials()">Testimonials</button>
  <button mat-menu-item routerLink="/auth/login">Login</button>
  <button mat-menu-item routerLink="/auth/church-registration">Get Started</button>
</mat-menu>

<!-- Hero Section -->
<section class="hero">
  <div class="hero-content">
    <div class="hero-text">
      <h1 class="hero-title">
        Streamline Your Church
        <span class="gradient-text">Attendance Management</span>
      </h1>
      <p class="hero-subtitle">
        Modern, digital attendance tracking for churches. Real-time check-ins,
        comprehensive reporting, and member management - all in one powerful platform.
      </p>
      <div class="hero-actions">
        <button mat-raised-button class="cta-primary" routerLink="/auth/signup">
          <mat-icon>rocket_launch</mat-icon>
          Start Free Trial
        </button>
        <button mat-stroked-button class="cta-secondary" (click)="scrollToFeatures()">
          <mat-icon>play_arrow</mat-icon>
          Learn More
        </button>
      </div>
      <div class="app-downloads">
        <p class="download-text">Download our mobile app:</p>
        <div class="download-buttons">
          <button class="app-store-btn android-btn direct-download" [disabled]="isDownloading || !apkAvailable"
            (click)="onDownloadApp($event)">
            @if (isDownloading) {
            <mat-spinner diameter="20" color="accent"></mat-spinner>
            } @else {
            <mat-icon class="store-icon">android</mat-icon>
            }
            <div class="store-text">
              <span class="download-on">
                @if (isDownloading) {
                Downloading...
                } @else if (!apkAvailable) {
                Unavailable
                } @else {
                Download for
                }
              </span>
              <span class="store-name">
                Android
                @if (apkFileSize && !isDownloading) {
                <small class="file-size">({{ apkFileSize }})</small>
                }
              </span>
            </div>
          </button>
          <button class="app-store-btn ios-btn disabled" disabled>
            <mat-icon class="store-icon">phone_iphone</mat-icon>
            <div class="store-text">
              <span class="download-on">Coming Soon</span>
              <span class="store-name">iOS App</span>
            </div>
          </button>
        </div>
        <div class="download-info">
          <mat-icon class="info-icon">info</mat-icon>
          <p class="info-text">
            <strong>Android users:</strong> You'll need to enable "Install from unknown sources" in your device settings
            to install the app.
            <a href="#" (click)="showInstallInstructions()" class="help-link">Need help?</a>
          </p>
        </div>
      </div>
    </div>
    <div class="hero-visual">
      <div class="phone-mockup">
        <div class="phone-screen">
          <div class="app-interface">
            <div class="app-header">
              <div class="status-bar"></div>
              <h3>Today's Service</h3>
            </div>
            <div class="check-in-card">
              <mat-icon class="check-icon">check_circle</mat-icon>
              <p>Checked in successfully!</p>
              <span class="time">9:45 AM</span>
            </div>
            <div class="stats">
              <div class="stat">
                <span class="number">247</span>
                <span class="label">Present</span>
              </div>
              <div class="stat">
                <span class="number">23</span>
                <span class="label">New Visitors</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Features Section -->
<section id="features" class="features">
  <div class="container">
    <div class="section-header">
      <h2>Powerful Features for Modern Churches</h2>
      <p>Everything you need to manage attendance and engage with your congregation</p>
    </div>
    <div class="features-grid">
      <mat-card class="feature-card" *ngFor="let feature of features">
        <mat-card-content>
          <div class="feature-icon">
            <mat-icon>{{ feature.icon }}</mat-icon>
          </div>
          <h3>{{ feature.title }}</h3>
          <p>{{ feature.description }}</p>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</section>

<!-- Benefits Section -->
<section class="benefits">
  <div class="container">
    <div class="benefits-content">
      <div class="benefits-text">
        <h2>Why Churches Choose Flockin</h2>
        <div class="benefit-list">
          <div class="benefit-item">
            <mat-icon class="benefit-icon">schedule</mat-icon>
            <div>
              <h4>Save Time</h4>
              <p>Reduce manual attendance tracking from hours to minutes</p>
            </div>
          </div>
          <div class="benefit-item">
            <mat-icon class="benefit-icon">insights</mat-icon>
            <div>
              <h4>Better Insights</h4>
              <p>Understand attendance patterns and member engagement</p>
            </div>
          </div>
          <div class="benefit-item">
            <mat-icon class="benefit-icon">groups</mat-icon>
            <div>
              <h4>Engage Members</h4>
              <p>Keep your congregation connected with digital tools</p>
            </div>
          </div>
        </div>
      </div>
      <div class="benefits-visual">
        <div class="dashboard-preview">
          <div class="dashboard-header">
            <h3>Church Dashboard</h3>
          </div>
          <div class="dashboard-stats">
            <div class="stat-card">
              <span class="stat-number">1,247</span>
              <span class="stat-label">Total Members</span>
            </div>
            <div class="stat-card">
              <span class="stat-number">89%</span>
              <span class="stat-label">Avg. Attendance</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Testimonials Section -->
<section id="testimonials" class="testimonials">
  <div class="container">
    <div class="section-header">
      <h2>Trusted by Churches Across Ghana</h2>
      <p>See what church leaders are saying about Flockin</p>
    </div>
    <div class="testimonials-grid">
      <mat-card class="testimonial-card" *ngFor="let testimonial of testimonials">
        <mat-card-content>
          <div class="quote-icon">
            <mat-icon>format_quote</mat-icon>
          </div>
          <p class="quote">"{{ testimonial.quote }}"</p>
          <div class="testimonial-author">
            <div class="author-avatar">
              <mat-icon>person</mat-icon>
            </div>
            <div class="author-info">
              <h4>{{ testimonial.name }}</h4>
              <p>{{ testimonial.church }}</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
  <div class="container">
    <div class="cta-content">
      <h2>Ready to Transform Your Church Attendance?</h2>
      <p>Join hundreds of churches already using Flockin to streamline their operations</p>
      <div class="cta-actions">
        <button mat-raised-button class="cta-primary" routerLink="/auth/signup">
          <mat-icon>rocket_launch</mat-icon>
          Start Your Free Trial
        </button>
        <button mat-stroked-button class="cta-secondary" routerLink="/auth/login">
          <mat-icon>login</mat-icon>
          Sign In
        </button>
      </div>
      <p class="cta-note">No credit card required • 30-day free trial • Cancel anytime</p>
    </div>
  </div>
</section>

<!-- Footer -->
<footer class="footer">
  <div class="container">
    <div class="footer-content">
      <div class="footer-brand">
        <div class="logo">
          <mat-icon class="logo-icon">church</mat-icon>
          <span class="logo-text">{{ appName }}</span>
        </div>
        <p>Modern church attendance management for the digital age.</p>
      </div>
      <div class="footer-links">
        <div class="link-group">
          <h4>Product</h4>
          <a href="#features">Features</a>
          <a href="#testimonials">Testimonials</a>
          <a routerLink="/auth/church-registration">Get Started</a>
        </div>
        <div class="link-group">
          <h4>Support</h4>
          <a href="mailto:<EMAIL>">Contact Us</a>
          <a href="#">Help Center</a>
          <a href="#">Documentation</a>
        </div>
        <div class="link-group">
          <h4>Company</h4>
          <a href="#">About Us</a>
          <a href="#">Privacy Policy</a>
          <a href="#">Terms of Service</a>
        </div>
      </div>
    </div>
    <div class="footer-bottom">
      <p>&copy; 2024 Flockin Dashboard. All rights reserved.</p>
      <div class="social-links">
        <button mat-icon-button>
          <mat-icon>facebook</mat-icon>
        </button>
        <button mat-icon-button>
          <mat-icon>twitter</mat-icon>
        </button>
        <button mat-icon-button>
          <mat-icon>linkedin</mat-icon>
        </button>
      </div>
    </div>
  </div>
</footer>