rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function getUserData() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
    }

    function isSuperAdmin() {
      return isAuthenticated() && getUserData().role == 'super_admin';
    }

    function isChurchAdmin(churchId) {
      return isAuthenticated() &&
             getUserData().churchId == churchId &&
             getUserData().role in ['admin', 'pastor'];
    }

    function isChurchMember(churchId) {
      return isAuthenticated() && getUserData().churchId == churchId;
    }

    function isSameUser(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Users collection
    match /users/{userId} {
      allow read: if isSameUser(userId) ||
                     isSuperAdmin() ||
                     isChurchAdmin(getUserData().churchId);
      allow write: if isSameUser(userId) ||
                      isSuperAdmin() ||
                      (isChurchAdmin(resource.data.churchId) && resource.data.churchId == getUserData().churchId);
      allow create: if isAuthenticated();
      allow delete: if isSuperAdmin() ||
                       (isChurchAdmin(resource.data.churchId) && resource.data.churchId == getUserData().churchId);
    }

    // Churches collection
    match /churches/{churchId} {
      allow read: if isAuthenticated();
      allow write: if isSuperAdmin() || isChurchAdmin(churchId);
      allow create: if isSuperAdmin() ||
                      (isAuthenticated() && getUserData().role == 'admin' && getUserData().churchId == null);
    }

    // Attendance collection
    match /attendance/{attendanceId} {
      allow read: if isChurchMember(resource.data.churchId) ||
                     isChurchAdmin(resource.data.churchId);
      allow create: if isAuthenticated() &&
                       request.auth.uid == request.resource.data.memberId &&
                       isChurchMember(request.resource.data.churchId);
      allow update: if isChurchMember(resource.data.churchId) &&
                       (request.auth.uid == resource.data.memberId ||
                        isChurchAdmin(resource.data.churchId));
    }

    // Church videos collection
    match /church_videos/{videoId} {
      allow read: if isChurchMember(resource.data.churchId);
      allow write: if isChurchAdmin(resource.data.churchId);
    }

    // Notifications collection - temporarily permissive for testing
    match /notifications/{notificationId} {
      allow read, write: if isAuthenticated();
    }

    // FCM Tokens collection - temporarily permissive for testing
    match /fcmTokens/{tokenId} {
      allow read, write: if isAuthenticated();
    }
  }
}
