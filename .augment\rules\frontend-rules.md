---
type: "always_apply"
description: "Frontend development guidelines for the Church Attendance Tracking App Dashboard using Angular, covering architecture, component organization, and module design."
---
You are a senior Software Engineer with PdD in Software Engineering with extensive experience in Angular and Firebase.

# Frontend Development Rules - Angular
## Church Attendance Tracking App Dashboard

---

## 🏗️ Architecture & Structure

### Component Organization
- **Feature modules**: Group related components, services, and pipes
- **Lazy loading**: Load feature modules on demand
- **Barrel exports**: Use index.ts for clean imports
- **Smart/Dumb components**: Container components manage state, presentational components display data

Only use `pnpm`.

Remember to be committing your changes, so that in case you make a mistake, you can always reset.

```typescript
// ✅ Good structure
src/app/
├── core/                 # Singleton services, guards
├── shared/              # Reusable components, pipes, directives
├── features/
│   ├── attendance/
│   │   ├── components/
│   │   ├── services/
│   │   └── attendance.module.ts
│   └── churches/
└── layout/              # App shell components
```

### Module Design
- **Core module**: Import once in AppModule
- **Shared module**: Import in feature modules
- **Feature modules**: Self-contained business domains
- **Routing modules**: Separate routing configuration

---

## 📝 Code Quality Standards

### TypeScript Best Practices
- **Strict mode**: Enable strict TypeScript compilation
- **Explicit types**: Declare types for all public properties and methods
- **Interfaces over classes**: Use interfaces for data models
- **Readonly properties**: Use readonly for immutable data

```typescript
// ✅ Good
interface AttendanceRecord {
  readonly id: string;
  readonly memberId: string;
  readonly checkInTime: Date;
  readonly location: GeolocationCoordinates;
}

@Component({...})
export class AttendanceListComponent implements OnInit {
  attendanceRecords: AttendanceRecord[] = [];
  
  constructor(private attendanceService: AttendanceService) {}
  
  ngOnInit(): void {
    this.loadAttendanceRecords();
  }
}
```

### Component Best Practices
- **OnPush change detection**: Use for performance optimization
- **Lifecycle hooks**: Implement proper cleanup in ngOnDestroy
- **Input validation**: Validate @Input() properties
- **Output naming**: Use descriptive event names

---

## 🎨 UI/UX Standards

### Angular Material Design
- **Consistent theming**: Use Angular Material theme system
- **Responsive design**: Mobile-first approach with breakpoints
- **Accessibility**: Follow WCAG 2.1 guidelines
- **Loading states**: Show loading indicators for async operations

```typescript
// ✅ Good responsive design
@Component({
  template: `
    <mat-card class="attendance-card">
      <mat-card-header>
        <mat-card-title>Live Attendance</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="attendance-grid" fxLayout="row wrap" fxLayoutGap="16px">
          <div *ngFor="let record of attendanceRecords" 
               fxFlex="100" fxFlex.gt-sm="50" fxFlex.gt-md="33">
            <!-- Attendance record content -->
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  `
})
```

### Performance Optimization
- **TrackBy functions**: Use with *ngFor for large lists
- **OnPush strategy**: Reduce change detection cycles
- **Lazy loading**: Load routes and modules on demand
- **Image optimization**: Use proper image formats and sizes

---

## 🔄 State Management

### Service-Based State
- **Injectable services**: Use services for shared state
- **BehaviorSubject**: For reactive state management
- **Immutable updates**: Never mutate state directly
- **Error handling**: Handle service errors gracefully

```typescript
// ✅ Good state service
@Injectable({ providedIn: 'root' })
export class AttendanceStateService {
  private attendanceSubject = new BehaviorSubject<AttendanceRecord[]>([]);
  public attendance$ = this.attendanceSubject.asObservable();
  
  updateAttendance(records: AttendanceRecord[]): void {
    this.attendanceSubject.next([...records]); // Immutable update
  }
  
  addAttendanceRecord(record: AttendanceRecord): void {
    const current = this.attendanceSubject.value;
    this.attendanceSubject.next([...current, record]);
  }
}
```

### Real-time Updates
- **WebSocket integration**: Use for live attendance updates
- **Reconnection logic**: Handle connection drops gracefully
- **Optimistic updates**: Update UI immediately, sync with server
- **Conflict resolution**: Handle concurrent updates

---

## 🔒 Security & Authentication

### JWT Token Management
- **Token storage**: Use httpOnly cookies or secure storage
- **Token refresh**: Implement automatic token renewal
- **Route guards**: Protect routes with authentication guards
- **Role-based access**: Implement authorization guards

```typescript
// ✅ Good auth guard
@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}
  
  canActivate(): Observable<boolean> {
    return this.authService.isAuthenticated$.pipe(
      tap(isAuth => {
        if (!isAuth) {
          this.router.navigate(['/login']);
        }
      })
    );
  }
}
```

### Input Sanitization
- **Form validation**: Use reactive forms with validators
- **XSS prevention**: Sanitize user inputs
- **CSRF protection**: Implement CSRF tokens
- **Content Security Policy**: Configure CSP headers

---

## 🌐 HTTP & API Integration

### HTTP Client Best Practices
- **Interceptors**: Use for authentication, error handling, loading
- **Error handling**: Centralized error handling service
- **Retry logic**: Implement retry for failed requests
- **Caching**: Cache static data appropriately

```typescript
// ✅ Good HTTP service
@Injectable({ providedIn: 'root' })
export class AttendanceApiService {
  private readonly baseUrl = environment.apiUrl;
  
  constructor(private http: HttpClient) {}
  
  getAttendanceRecords(churchId: string): Observable<AttendanceRecord[]> {
    return this.http.get<AttendanceRecord[]>(`${this.baseUrl}/attendance/${churchId}`)
      .pipe(
        retry(3),
        catchError(this.handleError)
      );
  }
  
  private handleError(error: HttpErrorResponse): Observable<never> {
    // Centralized error handling
    return throwError(() => new Error('Something went wrong'));
  }
}
```

---

## 🧪 Testing Standards

### Unit Testing
- **Component testing**: Test component logic and templates
- **Service testing**: Mock HTTP calls and dependencies
- **Pipe testing**: Test custom pipes thoroughly
- **Guard testing**: Test route protection logic

```typescript
// ✅ Good component test
describe('AttendanceListComponent', () => {
  let component: AttendanceListComponent;
  let fixture: ComponentFixture<AttendanceListComponent>;
  let mockAttendanceService: jasmine.SpyObj<AttendanceService>;
  
  beforeEach(() => {
    const spy = jasmine.createSpyObj('AttendanceService', ['getAttendance']);
    
    TestBed.configureTestingModule({
      declarations: [AttendanceListComponent],
      providers: [{ provide: AttendanceService, useValue: spy }]
    });
    
    mockAttendanceService = TestBed.inject(AttendanceService) as jasmine.SpyObj<AttendanceService>;
  });
  
  it('should load attendance records on init', () => {
    const mockRecords: AttendanceRecord[] = [/* mock data */];
    mockAttendanceService.getAttendance.and.returnValue(of(mockRecords));
    
    component.ngOnInit();
    
    expect(component.attendanceRecords).toEqual(mockRecords);
  });
});
```

### E2E Testing
- **User workflows**: Test complete user journeys
- **Cross-browser testing**: Test on different browsers
- **Mobile testing**: Test responsive behavior
- **Performance testing**: Monitor load times and interactions

---

## 📱 Responsive Design Rules

### Mobile-First Approach
- **Breakpoint strategy**: Design for mobile, enhance for desktop
- **Touch-friendly**: Minimum 44px touch targets
- **Navigation**: Collapsible navigation for mobile
- **Performance**: Optimize for slower mobile networks

### Accessibility Standards
- **ARIA labels**: Proper labeling for screen readers
- **Keyboard navigation**: Full keyboard accessibility
- **Color contrast**: Meet WCAG contrast requirements
- **Focus management**: Proper focus indicators

---

## 🚀 Performance Optimization

### Bundle Optimization
- **Tree shaking**: Remove unused code
- **Code splitting**: Split bundles by routes
- **Lazy loading**: Load modules on demand
- **Service workers**: Implement caching strategies

### Runtime Performance
- **Change detection**: Use OnPush where possible
- **Virtual scrolling**: For large lists
- **Image lazy loading**: Load images on demand
- **Memory leaks**: Unsubscribe from observables

```typescript
// ✅ Good subscription management
export class AttendanceComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  ngOnInit(): void {
    this.attendanceService.attendance$
      .pipe(takeUntil(this.destroy$))
      .subscribe(records => {
        this.attendanceRecords = records;
      });
  }
  
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
```

---

## 🌍 Ghana-Specific Considerations

### Network Optimization
- **Offline support**: Implement service worker caching
- **Progressive loading**: Load critical content first
- **Data compression**: Minimize API payload sizes
- **Connection awareness**: Adapt to network conditions

### Localization
- **i18n support**: Prepare for English/Twi localization
- **Date formatting**: Use Ghana date/time formats
- **Number formatting**: Support local number formats
- **RTL support**: Consider future Arabic support

### User Experience
- **Low-end device support**: Optimize for budget smartphones
- **Data usage**: Minimize data consumption
- **Battery optimization**: Reduce CPU-intensive operations
- **Intuitive UI**: Design for varying digital literacy levels

---

## 🔧 Development Workflow

### Code Review Checklist
- [ ] Components follow single responsibility principle
- [ ] Proper TypeScript typing throughout
- [ ] Accessibility standards met
- [ ] Mobile responsiveness tested
- [ ] Performance implications considered
- [ ] Unit tests written and passing
- [ ] No console.log statements in production code

### Build & Deployment
- **Environment configuration**: Separate configs for dev/staging/prod
- **Build optimization**: Enable production optimizations
- **Error monitoring**: Integrate error tracking service
- **Analytics**: Track user interactions and performance

---

*These rules ensure our Angular dashboard is performant, accessible, and user-friendly for Ghanaian church leaders.*
