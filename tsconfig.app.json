/* To learn more about Typescript configuration file: https://www.typescriptlang.org/docs/handbook/tsconfig-json.html. */
/* To learn more about Angular compiler options: https://angular.dev/reference/configs/angular-compiler-options. */
{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "outDir": "./out-tsc/app",
    "types": [],
    "baseUrl": "./",
    "paths": {
      "@/*": [
        "src/*"
      ],
      "@app/*": [
        "src/app/*"
      ],
      "@core/*": [
        "src/app/core/*"
      ],
      "@shared/*": [
        "src/app/shared/*"
      ],
      "@features/*": [
        "src/app/features/*"
      ],
      "@environments/*": [
        "src/environments/*"
      ]
    }
  },
  "include": [
    "src/**/*.ts"
  ],
  "exclude": [
    "src/**/*.spec.ts"
  ]
}