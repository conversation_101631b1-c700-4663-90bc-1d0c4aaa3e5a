import { Injectable, signal } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import { AttendanceService, CheckInRequest } from './attendance.service';
import { AuthService } from './auth.service';

export interface GeofenceStatus {
  isInside: boolean;
  distance: number;
  accuracy: number;
  lastChecked: Date;
  churchLocation?: {
    latitude: number;
    longitude: number;
    radius: number;
  };
}

export interface GeofenceConfig {
  churchLatitude: number;
  churchLongitude: number;
  radius: number; // in meters
  autoCheckIn: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class GeofenceService {
  private geofenceStatusSubject = new BehaviorSubject<GeofenceStatus>({
    isInside: false,
    distance: 0,
    accuracy: 0,
    lastChecked: new Date()
  });

  private watchId: number | null = null;
  private lastCheckInTime: Date | null = null;
  private checkInCooldown = 5 * 60 * 1000; // 5 minutes cooldown between check-ins

  // Signals for reactive UI
  isMonitoring = signal<boolean>(false);
  currentStatus = signal<GeofenceStatus>({
    isInside: false,
    distance: 0,
    accuracy: 0,
    lastChecked: new Date()
  });
  error = signal<string | null>(null);

  public geofenceStatus$ = this.geofenceStatusSubject.asObservable();

  constructor(
    private attendanceService: AttendanceService,
    private authService: AuthService
  ) {}

  /**
   * Start monitoring geofence
   */
  startMonitoring(config: GeofenceConfig): Observable<GeofenceStatus> {
    if (!navigator.geolocation) {
      this.error.set('Geolocation is not supported by this browser');
      return this.geofenceStatus$;
    }

    this.isMonitoring.set(true);
    this.error.set(null);

    const options: PositionOptions = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 30000 // 30 seconds
    };

    this.watchId = navigator.geolocation.watchPosition(
      (position) => {
        this.handlePositionUpdate(position, config);
      },
      (error) => {
        this.handleGeolocationError(error);
      },
      options
    );

    console.log('🌍 Geofence monitoring started');
    return this.geofenceStatus$;
  }

  /**
   * Stop monitoring geofence
   */
  stopMonitoring(): void {
    if (this.watchId !== null) {
      navigator.geolocation.clearWatch(this.watchId);
      this.watchId = null;
    }

    this.isMonitoring.set(false);
    console.log('🌍 Geofence monitoring stopped');
  }

  /**
   * Check if user is currently inside geofence
   */
  checkCurrentLocation(config: GeofenceConfig): Promise<GeofenceStatus> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation not supported'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const status = this.calculateGeofenceStatus(position, config);
          this.updateStatus(status);
          resolve(status);
        },
        (error) => {
          reject(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      );
    });
  }

  /**
   * Manually trigger check-in (for testing or manual check-in)
   */
  manualCheckIn(latitude: number, longitude: number, accuracy: number): Observable<any> {
    const checkInData: CheckInRequest = {
      latitude,
      longitude,
      accuracy,
      method: 'manual',
      serviceType: 'Sunday Service'
    };

    console.log('📍 Manual check-in triggered:', checkInData);
    return this.attendanceService.checkIn(checkInData);
  }

  private handlePositionUpdate(position: GeolocationPosition, config: GeofenceConfig): void {
    const status = this.calculateGeofenceStatus(position, config);
    this.updateStatus(status);

    // Auto check-in if enabled and user just entered geofence
    if (config.autoCheckIn && status.isInside && this.shouldAutoCheckIn()) {
      this.performAutoCheckIn(position, config);
    }
  }

  private calculateGeofenceStatus(position: GeolocationPosition, config: GeofenceConfig): GeofenceStatus {
    const distance = this.calculateDistance(
      position.coords.latitude,
      position.coords.longitude,
      config.churchLatitude,
      config.churchLongitude
    );

    const isInside = distance <= config.radius;

    return {
      isInside,
      distance: Math.round(distance),
      accuracy: Math.round(position.coords.accuracy),
      lastChecked: new Date(),
      churchLocation: {
        latitude: config.churchLatitude,
        longitude: config.churchLongitude,
        radius: config.radius
      }
    };
  }

  private updateStatus(status: GeofenceStatus): void {
    this.currentStatus.set(status);
    this.geofenceStatusSubject.next(status);

    // Log status changes
    const statusText = status.isInside ? '✅ Inside church area' : '❌ Outside church area';
    console.log(`🌍 Geofence status: ${statusText} (${status.distance}m away, ±${status.accuracy}m accuracy)`);
  }

  private shouldAutoCheckIn(): boolean {
    if (!this.lastCheckInTime) return true;
    
    const timeSinceLastCheckIn = Date.now() - this.lastCheckInTime.getTime();
    return timeSinceLastCheckIn > this.checkInCooldown;
  }

  private performAutoCheckIn(position: GeolocationPosition, config: GeofenceConfig): void {
    const checkInData: CheckInRequest = {
      latitude: position.coords.latitude,
      longitude: position.coords.longitude,
      accuracy: position.coords.accuracy,
      method: 'geofence',
      serviceType: 'Sunday Service'
    };

    console.log('🎯 Auto check-in triggered by geofence:', checkInData);

    this.attendanceService.checkIn(checkInData).subscribe({
      next: (result) => {
        this.lastCheckInTime = new Date();
        console.log('✅ Auto check-in successful:', result.message);
        
        // You could emit an event here to show a notification to the user
        // this.showCheckInNotification(result);
      },
      error: (error) => {
        console.error('❌ Auto check-in failed:', error);
        this.error.set(`Check-in failed: ${error.message}`);
      }
    });
  }

  private handleGeolocationError(error: GeolocationPositionError): void {
    let errorMessage = 'Location access error';
    
    switch (error.code) {
      case error.PERMISSION_DENIED:
        errorMessage = 'Location access denied by user';
        break;
      case error.POSITION_UNAVAILABLE:
        errorMessage = 'Location information unavailable';
        break;
      case error.TIMEOUT:
        errorMessage = 'Location request timed out';
        break;
    }

    console.error('🌍 Geolocation error:', errorMessage);
    this.error.set(errorMessage);
    this.isMonitoring.set(false);
  }

  /**
   * Calculate distance between two coordinates using Haversine formula
   */
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // Distance in meters
  }

  /**
   * Get current geofence status
   */
  getCurrentStatus(): GeofenceStatus {
    return this.currentStatus();
  }

  /**
   * Check if monitoring is active
   */
  isCurrentlyMonitoring(): boolean {
    return this.isMonitoring();
  }
}
