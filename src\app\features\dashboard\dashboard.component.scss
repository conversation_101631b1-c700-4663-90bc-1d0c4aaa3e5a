.dashboard-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

.dashboard-header {
  margin-bottom: 3rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 2rem;
  color: white;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .welcome-title {
    margin: 0 0 0.5rem 0;
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .church-name {
    margin: 0;
    font-size: 1.1rem;
    opacity: 0.9;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .church-icon {
      font-size: 1.2rem;
    }
  }

  .refresh-fab {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }
  }

  .error-message {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    color: #e53e3e;
    font-size: 0.875rem;
    margin-top: 1.5rem;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    mat-icon {
      color: #e53e3e;
    }

    span {
      flex: 1;
    }

    button {
      border-radius: 8px;
    }
  }
}

// Section Titles
.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 1.5rem 0;

  mat-icon {
    color: #667eea;
    font-size: 1.5rem;
    width: 1.5rem;
    height: 1.5rem;
  }
}

.stats-section {
  margin-bottom: 3rem;
}

// Professional Stats Grid
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15) !important;
  }

  &.members-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    .stat-icon-container.members {
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
    }
  }

  &.attendance-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;

    .stat-icon-container.attendance {
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
    }
  }

  &.rate-card {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;

    .stat-icon-container.rate {
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
    }
  }

  &.weekly-card {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;

    .stat-icon-container.weekly {
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
    }
  }

  .stat-layout {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
  }

  .stat-icon-container {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .stat-icon {
      font-size: 28px;
      width: 28px;
      height: 28px;
      color: white;
    }
  }

  .stat-content {
    flex: 1;
    min-width: 0;

    .stat-value {
      font-size: 2.5rem;
      font-weight: 700;
      line-height: 1;
      margin-bottom: 0.5rem;
    }

    .stat-label {
      margin: 0 0 0.75rem 0;
      font-size: 0.875rem;
      font-weight: 500;
      opacity: 0.9;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .stat-trend {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      font-size: 0.75rem;
      font-weight: 500;
      opacity: 0.8;

      mat-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }

      &.positive {
        color: rgba(255, 255, 255, 0.9);
      }

      &.neutral {
        color: rgba(255, 255, 255, 0.8);
      }

      &.negative {
        color: rgba(255, 255, 255, 0.7);
      }
    }
  }
}

// Quick Actions Section
.actions-section {
  margin-bottom: 3rem;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1rem;
}

.action-card {
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid #e2e8f0;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12) !important;
    border-color: #667eea;
  }

  .action-layout {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
  }

  .action-icon-container {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .action-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
      color: white;
    }
  }

  .action-content {
    flex: 1;
    min-width: 0;

    .action-title {
      margin: 0 0 0.25rem 0;
      font-size: 1rem;
      font-weight: 600;
      color: #2d3748;
    }

    .action-description {
      margin: 0;
      font-size: 0.875rem;
      color: #718096;
      line-height: 1.4;
    }
  }

  .action-arrow {
    color: #a0aec0;
    transition: all 0.2s ease;
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

  &:hover .action-arrow {
    color: #667eea;
    transform: translateX(4px);
  }
}

// Activity Section
.activity-section {
  margin-bottom: 2rem;
}

.activity-card {
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  background: #f8fafc;
  transition: all 0.2s ease;

  &:hover {
    background: #edf2f7;
  }

  .activity-icon-container {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .activity-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      color: white;
    }
  }

  .activity-content {
    flex: 1;
    min-width: 0;

    .activity-text {
      font-size: 0.875rem;
      color: #2d3748;
      line-height: 1.4;
      margin-bottom: 0.25rem;

      strong {
        color: #1a202c;
        font-weight: 600;
      }
    }

    .activity-time {
      font-size: 0.75rem;
      color: #718096;
      font-weight: 500;
    }
  }
}

// Loading State
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #718096;

  p {
    margin: 1.5rem 0 0 0;
    font-size: 1rem;
    font-weight: 500;
  }
}

// Empty State
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;

  .empty-icon-container {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;

    .empty-icon {
      font-size: 40px;
      width: 40px;
      height: 40px;
      color: #a0aec0;
    }
  }

  h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #4a5568;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
    color: #718096;
    line-height: 1.5;
    max-width: 300px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.activity-list {
  .activity-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem 0;

    &:not(:last-child) {
      border-bottom: 1px solid #f7fafc;
    }

    .activity-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      .verified-icon {
        color: #38a169;
      }

      .flagged-icon {
        color: #e53e3e;
      }

      .default-icon {
        color: #4a5568;
      }
    }

    .activity-content {
      flex: 1;

      .activity-text {
        font-size: 0.875rem;
        color: #4a5568;
        margin-bottom: 0.25rem;

        .distance-chip {
          font-size: 0.75rem;
          margin-left: 0.5rem;
        }
      }

      .activity-time {
        font-size: 0.75rem;
        color: #a0aec0;
      }
    }
  }
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;

  .action-btn {
    justify-content: flex-start;
    text-align: left;

    mat-icon {
      margin-right: 0.5rem;
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
  }

  .quick-actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 0 0.5rem;
  }

  .dashboard-header {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border-radius: 12px;

    .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .welcome-title {
      font-size: 2rem;
    }

    .refresh-fab {
      align-self: flex-end;
    }
  }

  .section-title {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-card .stat-layout {
    padding: 1.25rem;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .action-card .action-layout {
    padding: 1.25rem;
  }

  .activity-item {
    padding: 0.75rem;
  }

  .stats-section,
  .actions-section {
    margin-bottom: 2rem;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: 1rem;

    .welcome-title {
      font-size: 1.75rem;
    }

    .church-name {
      font-size: 1rem;
    }
  }

  .stat-card {
    .stat-layout {
      padding: 1rem;
    }

    .stat-content .stat-value {
      font-size: 2rem;
    }
  }

  .action-card .action-layout {
    padding: 1rem;
  }

  .empty-state {
    padding: 2rem 1rem;

    .empty-icon-container {
      width: 60px;
      height: 60px;

      .empty-icon {
        font-size: 30px;
        width: 30px;
        height: 30px;
      }
    }
  }
}

// Animation keyframes
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Add subtle animations
.stat-card,
.action-card,
.activity-card {
  animation: fadeInUp 0.6s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }