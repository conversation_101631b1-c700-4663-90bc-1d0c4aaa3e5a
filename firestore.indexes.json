{"indexes": [{"collectionGroup": "attendance", "queryScope": "COLLECTION", "fields": [{"fieldPath": "churchId", "order": "ASCENDING"}, {"fieldPath": "checkInTime", "order": "DESCENDING"}]}, {"collectionGroup": "attendance", "queryScope": "COLLECTION", "fields": [{"fieldPath": "churchId", "order": "ASCENDING"}, {"fieldPath": "checkInTime", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "attendance", "queryScope": "COLLECTION", "fields": [{"fieldPath": "memberId", "order": "ASCENDING"}, {"fieldPath": "checkInTime", "order": "DESCENDING"}]}, {"collectionGroup": "attendance", "queryScope": "COLLECTION", "fields": [{"fieldPath": "churchId", "order": "ASCENDING"}, {"fieldPath": "serviceType", "order": "ASCENDING"}, {"fieldPath": "checkInTime", "order": "DESCENDING"}]}, {"collectionGroup": "church_videos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "churchId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "church_videos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "churchId", "order": "ASCENDING"}, {"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "churchId", "order": "ASCENDING"}, {"fieldPath": "role", "order": "ASCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "churchId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": [{"collectionGroup": "users", "fieldPath": "email", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}]}