import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

export interface ConfirmDeleteDialogData {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
}

@Component({
  selector: 'app-confirm-delete-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule
  ],
  template: `
    <div class="confirm-delete-dialog">
      <div class="dialog-header">
        <h2 mat-dialog-title>
          <mat-icon class="warning-icon">warning</mat-icon>
          {{ data.title }}
        </h2>
      </div>

      <mat-dialog-content class="dialog-content">
        <p>{{ data.message }}</p>
      </mat-dialog-content>

      <mat-dialog-actions class="dialog-actions">
        <button mat-button (click)="onCancel()">
          {{ data.cancelText || 'Cancel' }}
        </button>
        <button mat-raised-button color="warn" (click)="onConfirm()">
          <mat-icon>delete</mat-icon>
          {{ data.confirmText || 'Delete' }}
        </button>
      </mat-dialog-actions>
    </div>
  `,
  styles: [`
    .confirm-delete-dialog {
      width: 100%;
      max-width: 400px;
      
      .dialog-header {
        padding: 0 24px;
        border-bottom: 1px solid #e0e0e0;
        
        h2 {
          display: flex;
          align-items: center;
          gap: 8px;
          margin: 0;
          font-size: 1.25rem;
          font-weight: 500;
          color: #f44336;
          
          .warning-icon {
            font-size: 1.5rem;
            width: 1.5rem;
            height: 1.5rem;
            color: #ff9800;
          }
        }
      }
      
      .dialog-content {
        padding: 24px;
        
        p {
          margin: 0;
          font-size: 1rem;
          line-height: 1.5;
          color: #333;
        }
      }
      
      .dialog-actions {
        padding: 16px 24px;
        border-top: 1px solid #e0e0e0;
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        
        button {
          min-width: 80px;
          
          &[mat-raised-button] {
            display: flex;
            align-items: center;
            gap: 8px;
            
            mat-icon {
              font-size: 1rem;
              width: 1rem;
              height: 1rem;
            }
          }
        }
      }
    }

    // Responsive design
    @media (max-width: 768px) {
      .confirm-delete-dialog {
        max-width: 95vw;
        
        .dialog-actions {
          flex-direction: column-reverse;
          
          button {
            width: 100%;
          }
        }
      }
    }
  `]
})
export class ConfirmDeleteDialogComponent {
  constructor(
    private dialogRef: MatDialogRef<ConfirmDeleteDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmDeleteDialogData
  ) {}

  onConfirm(): void {
    this.dialogRef.close(true);
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
