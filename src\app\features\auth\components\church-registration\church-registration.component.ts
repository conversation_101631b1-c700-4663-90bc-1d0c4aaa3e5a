import { Component, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatStepperModule } from '@angular/material/stepper';

import { ChurchService } from '@core/services/church.service';
import { AuthService } from '@core/services/auth.service';
import { CreateChurchRequest, ChurchSettings, ServiceTime } from '@shared/models/church.model';
import { CreateUserRequest } from '@shared/models/user.model';

@Component({
  selector: 'app-church-registration',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatStepperModule
  ],
  templateUrl: './church-registration.component.html',
  styleUrls: ['./church-registration.component.scss']
})
export class ChurchRegistrationComponent {
  churchForm: FormGroup;
  adminForm: FormGroup;
  currentStep = signal(1);
  isLoading = signal(false);
  error = signal<string | null>(null);

  constructor(
    private fb: FormBuilder,
    private churchService: ChurchService,
    private authService: AuthService,
    private router: Router
  ) {
    this.churchForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      branch: [''],
      address: ['', [Validators.required]],
      description: [''],
      serviceTypes: [['Sunday Service', 'Bible Study']],
      serviceTimes: [this.getDefaultServiceTimes()]
    });

    this.adminForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });
  }

  passwordMatchValidator(control: any): { [key: string]: boolean } | null {
    const password = control.get('password');
    const confirmPassword = control.get('confirmPassword');

    if (!password || !confirmPassword) {
      return null;
    }

    return password.value === confirmPassword.value ? null : { passwordMismatch: true };
  }

  nextStep(): void {
    if (this.currentStep() === 1 && this.churchForm.valid) {
      this.currentStep.set(2);
    }
  }

  previousStep(): void {
    if (this.currentStep() === 2) {
      this.currentStep.set(1);
    }
  }

  async onSubmit(): Promise<void> {
    if (this.churchForm.invalid || this.adminForm.invalid) {
      this.markAllFormsTouched();
      return;
    }

    this.isLoading.set(true);
    this.error.set(null);

    try {
      // First create the admin user
      const adminData: CreateUserRequest = {
        firstName: this.adminForm.value.firstName,
        lastName: this.adminForm.value.lastName,
        email: this.adminForm.value.email,
        role: 'admin'
      };

      await this.authService.signUp(adminData, this.adminForm.value.password);

      // Get the created user ID
      const currentUser = this.authService.currentUser();
      if (!currentUser) {
        throw new Error('Failed to create admin user');
      }

      // Create church settings with default values
      const settings: ChurchSettings = {
        allowSelfRegistration: true, // Members will self-register from mobile app
        requireApproval: false, // No approval needed for mobile app registration
        geofenceRadius: 100 // Default value, can be updated later in profile
      };

      // Create the church with the admin user ID
      const churchData: CreateChurchRequest = {
        name: this.churchForm.value.name,
        branch: this.churchForm.value.branch,
        address: this.churchForm.value.address,
        latitude: 0, // Default value, will be set in profile
        longitude: 0, // Default value, will be set in profile
        description: this.churchForm.value.description,
        serviceTypes: this.churchForm.value.serviceTypes,
        serviceTimes: this.churchForm.value.serviceTimes,
        adminIds: [currentUser.id],
        settings
      };

      // Create the church and get the ID
      const churchId = await new Promise<string>((resolve, reject) => {
        this.churchService.createChurch(churchData).subscribe({
          next: (id) => resolve(id),
          error: (error) => reject(error)
        });
      });

      // Update the user with the church ID
      await this.authService.updateUserChurch(churchId);

      // Sign out the user so they can login with their credentials
      await this.authService.signOut();

      // Registration successful, redirect to login page
      this.router.navigate(['/auth/login'], {
        queryParams: {
          message: 'Church registration successful! Please login with your credentials.'
        }
      });
    } catch (error: any) {
      this.error.set(error.message || 'Registration failed. Please try again.');
    } finally {
      this.isLoading.set(false);
    }
  }

  private markAllFormsTouched(): void {
    this.markFormGroupTouched(this.churchForm);
    this.markFormGroupTouched(this.adminForm);
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(form: FormGroup, fieldName: string): string | null {
    const field = form.get(fieldName);
    if (field?.touched && field?.errors) {
      if (field.errors['required']) {
        return `${this.getFieldDisplayName(fieldName)} is required`;
      }
      if (field.errors['email']) {
        return 'Please enter a valid email address';
      }
      if (field.errors['minlength']) {
        const requiredLength = field.errors['minlength'].requiredLength;
        return `${this.getFieldDisplayName(fieldName)} must be at least ${requiredLength} characters`;
      }
      if (field.errors['min']) {
        return `${this.getFieldDisplayName(fieldName)} must be at least ${field.errors['min'].min}`;
      }
      if (field.errors['max']) {
        return `${this.getFieldDisplayName(fieldName)} must be at most ${field.errors['max'].max}`;
      }
    }

    if (fieldName === 'confirmPassword' && this.adminForm.errors?.['passwordMismatch'] && field?.touched) {
      return 'Passwords do not match';
    }

    return null;
  }

  private getFieldDisplayName(fieldName: string): string {
    const displayNames: { [key: string]: string } = {
      name: 'Church name',
      address: 'Address',
      latitude: 'Latitude',
      longitude: 'Longitude',
      description: 'Description',
      geofenceRadius: 'Geofence radius',
      firstName: 'First name',
      lastName: 'Last name',
      email: 'Email',
      password: 'Password',
      confirmPassword: 'Confirm password'
    };
    return displayNames[fieldName] || fieldName;
  }

  private getDefaultServiceTimes(): ServiceTime[] {
    return [
      {
        name: 'Sunday Morning Service',
        day: 'sunday',
        startTime: '09:00',
        endTime: '11:00'
      },
      {
        name: 'Wednesday Bible Study',
        day: 'wednesday',
        startTime: '18:00',
        endTime: '20:00'
      }
    ];
  }

  get appName(): string {
    return 'Flockin Dashboard';
  }
}
