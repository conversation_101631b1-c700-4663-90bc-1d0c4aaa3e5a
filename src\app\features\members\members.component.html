<div class="members-container">
  <!-- Header Section -->
  <div class="members-header">
    <div class="header-content">
      <h1 class="page-title">
        <mat-icon>people</mat-icon>
        Members Management
      </h1>
      <p class="page-subtitle">Manage your church members and their information</p>
    </div>

    <div class="header-actions">
      <button mat-raised-button color="primary" class="add-member-btn" (click)="onAddMember()" [disabled]="isLoading()">
        <mat-icon>person_add</mat-icon>
        Add Member
      </button>
      <button mat-raised-button color="accent" class="bulk-import-btn" (click)="onBulkImport()"
        [disabled]="isLoading()">
        <mat-icon>upload</mat-icon>
        Bulk Import
      </button>
      <button mat-icon-button (click)="onRefresh()" [disabled]="isLoading()" matTooltip="Refresh">
        <mat-icon>refresh</mat-icon>
      </button>
    </div>
  </div>

  <!-- Stats Cards -->
  @if (memberStats(); as stats) {
  <div class="stats-grid">
    <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon total">
            <mat-icon>groups</mat-icon>
          </div>
          <div class="stat-info">
            <h3>{{ stats.totalMembers }}</h3>
            <p>Total Members</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon active">
            <mat-icon>check_circle</mat-icon>
          </div>
          <div class="stat-info">
            <h3>{{ stats.activeMembers }}</h3>
            <p>Active Members</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon new">
            <mat-icon>person_add</mat-icon>
          </div>
          <div class="stat-info">
            <h3>{{ stats.newMembersThisMonth }}</h3>
            <p>New This Month</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <mat-card class="stat-card">
      <mat-card-content>
        <div class="stat-content">
          <div class="stat-icon recent">
            <mat-icon>schedule</mat-icon>
          </div>
          <div class="stat-info">
            <h3>{{ stats.recentlyActive.length }}</h3>
            <p>Recently Active</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
  }

  <!-- Search and Filters -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters-content">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Search members</mat-label>
          <input matInput placeholder="Search by name, email, or phone" [value]="searchQuery()"
            (input)="onSearchChange($any($event.target).value || '')">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <div class="filter-options">
          <mat-checkbox [checked]="includeInactive()" (change)="onIncludeInactiveChange()">
            Include inactive members
          </mat-checkbox>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Members Table -->
  <mat-card class="table-card">
    <mat-card-content>
      @if (isLoading()) {
      <div class="loading-container">
        <mat-spinner diameter="50"></mat-spinner>
        <p>Loading members...</p>
      </div>
      } @else if (filteredMembers().length === 0) {
      <div class="empty-state">
        <mat-icon>people_outline</mat-icon>
        <h3>No members found</h3>
        <p>
          @if (searchQuery()) {
          No members match your search criteria.
          } @else {
          Your church doesn't have any members yet.
          }
        </p>
      </div>
      } @else {
      <div class="table-container">
        <table mat-table [dataSource]="filteredMembers()" class="members-table" matSort>
          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
            <td mat-cell *matCellDef="let member">
              <div class="member-info">
                @if (member.profilePicture) {
                <img [src]="member.profilePicture" [alt]="member.fullName || (member.firstName + ' ' + member.lastName)"
                  class="member-avatar">
                } @else {
                <div class="member-avatar-placeholder">
                  {{ member.firstName.charAt(0) }}{{ member.lastName.charAt(0) }}
                </div>
                }
                <div class="member-details">
                  <span class="member-name">{{ member.fullName || (member.firstName + ' ' + member.lastName) }}</span>
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Email Column -->
          <ng-container matColumnDef="email">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
            <td mat-cell *matCellDef="let member">{{ member.email }}</td>
          </ng-container>

          <!-- Phone Column -->
          <ng-container matColumnDef="phone">
            <th mat-header-cell *matHeaderCellDef>Phone</th>
            <td mat-cell *matCellDef="let member">{{ member.phone || 'N/A' }}</td>
          </ng-container>

          <!-- Role Column -->
          <ng-container matColumnDef="role">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Role</th>
            <td mat-cell *matCellDef="let member">
              <mat-chip [color]="getRoleColor(member.role)">{{ member.role }}</mat-chip>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
            <td mat-cell *matCellDef="let member">
              <mat-chip [color]="getStatusColor(member.status || 'active')">{{ member.status || 'active' }}</mat-chip>
            </td>
          </ng-container>

          <!-- Last Login Column -->
          <ng-container matColumnDef="lastLogin">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Last Login</th>
            <td mat-cell *matCellDef="let member">{{ member.lastLoginAt ? formatDate(member.lastLoginAt) : 'Never' }}
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let member">
              <button mat-icon-button [matMenuTriggerFor]="memberMenu">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #memberMenu="matMenu">
                <button mat-menu-item (click)="onViewMember(member)">
                  <mat-icon>visibility</mat-icon>
                  <span>View Details</span>
                </button>
                <button mat-menu-item (click)="onEditMember(member)">
                  <mat-icon>edit</mat-icon>
                  <span>Edit</span>
                </button>
                <mat-divider></mat-divider>
                <button mat-menu-item (click)="onDeleteMember(member)" class="delete-action">
                  <mat-icon>delete</mat-icon>
                  <span>Remove</span>
                </button>
              </mat-menu>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>
      }
    </mat-card-content>
  </mat-card>
</div>