<div class="church-registration-container">
  <div class="registration-card">
    <div class="header">
      <h1>Register Your Church</h1>
      <p>Join {{ appName }} and start managing your church attendance</p>
    </div>

    <!-- Progress Indicator -->
    <div class="progress-indicator">
      <div class="step" [class.active]="currentStep() === 1" [class.completed]="currentStep() > 1">
        <span class="step-number">1</span>
        <span class="step-label">Church Details</span>
      </div>
      <div class="step" [class.active]="currentStep() === 2">
        <span class="step-number">2</span>
        <span class="step-label">Admin Account</span>
      </div>
    </div>

    <!-- Error Message -->
    @if (error()) {
    <div class="error-message">
      {{ error() }}
    </div>
    }

    <!-- Step 1: Church Information -->
    @if (currentStep() === 1) {
    <form [formGroup]="churchForm" class="registration-form">
      <div class="form-group">
        <label for="name">Church Name *</label>
        <input type="text" id="name" formControlName="name" placeholder="Enter your church name"
          [class.error]="getFieldError(churchForm, 'name')" />
        @if (getFieldError(churchForm, 'name')) {
        <span class="field-error">{{ getFieldError(churchForm, 'name') }}</span>
        }
      </div>

      <div class="form-group">
        <label for="branch">Branch/Location</label>
        <input type="text" id="branch" formControlName="branch"
          placeholder="e.g., Main Branch, Accra Branch, Kumasi Branch" />
        <small class="field-help">Optional: Specify the branch or location if your church has multiple branches</small>
      </div>

      <div class="form-group">
        <label for="address">Address *</label>
        <input type="text" id="address" formControlName="address" placeholder="Enter church address"
          [class.error]="getFieldError(churchForm, 'address')" />
        @if (getFieldError(churchForm, 'address')) {
        <span class="field-error">{{ getFieldError(churchForm, 'address') }}</span>
        }
      </div>

      <div class="form-group">
        <label for="description">Description</label>
        <textarea id="description" formControlName="description" placeholder="Brief description of your church"
          rows="3"></textarea>
        <small class="field-help">Coordinates and geofence settings can be configured later in your church
          profile</small>
      </div>



      <div class="form-actions">
        <button type="button" class="btn btn-primary" (click)="nextStep()" [disabled]="churchForm.invalid">
          Next Step
        </button>
      </div>
    </form>
    }

    <!-- Step 2: Admin Account -->
    @if (currentStep() === 2) {
    <form [formGroup]="adminForm" class="registration-form">
      <div class="form-row">
        <div class="form-group">
          <label for="firstName">First Name *</label>
          <input type="text" id="firstName" formControlName="firstName" placeholder="Enter first name"
            [class.error]="getFieldError(adminForm, 'firstName')" />
          @if (getFieldError(adminForm, 'firstName')) {
          <span class="field-error">{{ getFieldError(adminForm, 'firstName') }}</span>
          }
        </div>

        <div class="form-group">
          <label for="lastName">Last Name *</label>
          <input type="text" id="lastName" formControlName="lastName" placeholder="Enter last name"
            [class.error]="getFieldError(adminForm, 'lastName')" />
          @if (getFieldError(adminForm, 'lastName')) {
          <span class="field-error">{{ getFieldError(adminForm, 'lastName') }}</span>
          }
        </div>
      </div>

      <div class="form-group">
        <label for="email">Email Address *</label>
        <input type="email" id="email" formControlName="email" placeholder="Enter email address"
          [class.error]="getFieldError(adminForm, 'email')" />
        @if (getFieldError(adminForm, 'email')) {
        <span class="field-error">{{ getFieldError(adminForm, 'email') }}</span>
        }
      </div>

      <div class="form-group">
        <label for="password">Password *</label>
        <input type="password" id="password" formControlName="password" placeholder="Enter password (min 8 characters)"
          [class.error]="getFieldError(adminForm, 'password')" />
        @if (getFieldError(adminForm, 'password')) {
        <span class="field-error">{{ getFieldError(adminForm, 'password') }}</span>
        }
      </div>

      <div class="form-group">
        <label for="confirmPassword">Confirm Password *</label>
        <input type="password" id="confirmPassword" formControlName="confirmPassword"
          placeholder="Confirm your password" [class.error]="getFieldError(adminForm, 'confirmPassword')" />
        @if (getFieldError(adminForm, 'confirmPassword')) {
        <span class="field-error">{{ getFieldError(adminForm, 'confirmPassword') }}</span>
        }
      </div>

      <div class="form-actions">
        <button type="button" class="btn btn-secondary" (click)="previousStep()">
          Previous
        </button>
        <button type="submit" class="btn btn-primary" (click)="onSubmit()"
          [disabled]="isLoading() || adminForm.invalid">
          @if (isLoading()) {
          <span class="loading-spinner"></span>
          Creating Account...
          } @else {
          Register Church
          }
        </button>
      </div>
    </form>
    }

    <div class="footer">
      <p>Already have an account? <a routerLink="/auth/login">Sign in here</a></p>
    </div>
  </div>
</div>