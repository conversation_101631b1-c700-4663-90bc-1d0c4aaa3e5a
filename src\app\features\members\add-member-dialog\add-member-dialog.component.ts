import { Component, Inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar } from '@angular/material/snack-bar';

import { MembersService } from '@core/services/members.service';
import { AuthService } from '@core/services/auth.service';
import { CreateUserRequest } from '@shared/models/user.model';

export interface AddMemberDialogData {
  churchId: string;
}

@Component({
  selector: 'app-add-member-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './add-member-dialog.component.html',
  styleUrl: './add-member-dialog.component.scss'
})
export class AddMemberDialogComponent {
  memberForm: FormGroup;
  isLoading = signal<boolean>(false);
  showPassword = signal<boolean>(false);
  showConfirmPassword = signal<boolean>(false);

  genderOptions = [
    { value: 'male', label: 'Male' },
    { value: 'female', label: 'Female' },
    { value: 'other', label: 'Other' }
  ];

  roleOptions = [
    { value: 'member', label: 'Member' },
    { value: 'admin', label: 'Church Admin' },
    { value: 'pastor', label: 'Pastor' }
  ];

  constructor(
    private fb: FormBuilder,
    private membersService: MembersService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private dialogRef: MatDialogRef<AddMemberDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AddMemberDialogData
  ) {
    this.memberForm = this.createForm();
  }

  private createForm(): FormGroup {
    return this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: [''],
      dateOfBirth: [''],
      gender: [''],
      address: [''],
      password: ['', [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', [Validators.required]],
      role: ['member', [Validators.required]],
      emergencyContactName: [''],
      emergencyContactPhone: ['']
    }, { validators: this.passwordMatchValidator });
  }

  private passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }

    return null;
  }

  onSubmit(): void {
    if (this.memberForm.valid && !this.isLoading()) {
      this.isLoading.set(true);

      const formValue = this.memberForm.value;
      const memberData: CreateUserRequest = {
        firstName: formValue.firstName,
        lastName: formValue.lastName,
        email: formValue.email,
        password: formValue.password,
        phone: formValue.phone || undefined,
        dateOfBirth: formValue.dateOfBirth || undefined,
        gender: formValue.gender || undefined,
        address: formValue.address || undefined,
        emergencyContactName: formValue.emergencyContactName || undefined,
        emergencyContactPhone: formValue.emergencyContactPhone || undefined,
        churchId: this.data.churchId,
        role: formValue.role as 'super_admin' | 'admin' | 'pastor' | 'member',
        status: 'active'
      };

      console.log('🔧 Creating member with Firebase Auth + Firestore...');
      console.log('📝 Form values:', formValue);
      console.log('👤 Member data being sent:', memberData);
      console.log('📞 Phone value specifically:', memberData.phone);
      console.log('🔐 Password provided:', !!memberData.password);

      this.membersService.createMember(memberData).subscribe({
        next: (response) => {
          this.isLoading.set(false);
          console.log('✅ Member created successfully with login credentials!', response);
          this.snackBar.open('Member added successfully with login credentials!', 'Close', {
            duration: 4000,
            panelClass: ['success-snackbar']
          });
          this.dialogRef.close(response.member);
        },
        error: (error) => {
          this.isLoading.set(false);
          const errorMessage = error.error?.message || 'Failed to add member. Please try again.';
          this.snackBar.open(errorMessage, 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  getFieldError(fieldName: string): string {
    const field = this.memberForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['email']) return 'Please enter a valid email';
      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
      if (field.errors['passwordMismatch']) return 'Passwords do not match';
    }
    return '';
  }

  hasFieldError(fieldName: string): boolean {
    const field = this.memberForm.get(fieldName);
    return !!(field && field.errors && field.touched);
  }

  togglePasswordVisibility(): void {
    this.showPassword.set(!this.showPassword());
  }

  toggleConfirmPasswordVisibility(): void {
    this.showConfirmPassword.set(!this.showConfirmPassword());
  }
}
