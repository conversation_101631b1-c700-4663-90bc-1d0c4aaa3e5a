import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatCardModule } from '@angular/material/card';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { environment } from '../../../environments/environment';
import { DownloadService, DownloadResult } from '../../core/services/download.service';

@Component({
  selector: 'app-landing',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatButtonModule,
    MatIconModule,
    MatToolbarModule,
    MatCardModule,
    MatMenuModule,
    MatDialogModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './landing.component.html',
  styleUrl: './landing.component.scss'
})
export class LandingComponent implements OnInit {
  appName = environment.appName;
  isDownloading = false;
  apkAvailable = true; // Always true for Dropbox hosting
  apkFileSize = '';

  constructor(
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private downloadService: DownloadService
  ) { }

  features = [
    {
      icon: 'people',
      title: 'Real-time Attendance',
      description: 'Track member attendance instantly with digital check-ins and live updates during services.'
    },
    {
      icon: 'location_on',
      title: 'Geofence Technology',
      description: 'Automatic attendance marking when members enter your church premises using GPS technology.'
    },
    {
      icon: 'analytics',
      title: 'Comprehensive Reports',
      description: 'Generate detailed attendance reports and analytics to understand your congregation better.'
    },
    {
      icon: 'group',
      title: 'Member Management',
      description: 'Easily manage your congregation with detailed member profiles and engagement tracking.'
    },
    {
      icon: 'smartphone',
      title: 'Mobile-First Design',
      description: 'Beautiful mobile apps for both iOS and Android, designed for ease of use by all age groups.'
    },
    {
      icon: 'security',
      title: 'Secure & Reliable',
      description: 'Enterprise-grade security with reliable cloud infrastructure to protect your church data.'
    }
  ];

  testimonials = [
    {
      name: 'Pastor John Mensah',
      church: 'Grace Baptist Church, Accra',
      quote: 'Flockin has transformed how we track attendance. What used to take hours now happens automatically.',
      avatar: 'assets/images/testimonial-1.jpg'
    },
    {
      name: 'Rev. Sarah Osei',
      church: 'New Life Methodist Church, Kumasi',
      quote: 'The mobile app makes it so easy for our members to check in. Attendance has never been more accurate.',
      avatar: 'assets/images/testimonial-2.jpg'
    },
    {
      name: 'Elder Michael Asante',
      church: 'Christ Embassy, Tema',
      quote: 'The reporting features help us understand our congregation better and plan our services accordingly.',
      avatar: 'assets/images/testimonial-3.jpg'
    }
  ];

  ngOnInit(): void {
    // Check APK availability on Dropbox
    this.checkAPKAvailability();
  }

  scrollToFeatures(): void {
    const element = document.getElementById('features');
    element?.scrollIntoView({ behavior: 'smooth' });
  }

  scrollToTestimonials(): void {
    const element = document.getElementById('testimonials');
    element?.scrollIntoView({ behavior: 'smooth' });
  }

  checkAPKAvailability(): void {
    console.log('🔍 Checking APK availability on Dropbox...');
    this.downloadService.checkAPKAvailability().subscribe({
      next: (result) => {
        console.log('✅ Dropbox APK check result:', result);
        this.apkAvailable = result.exists;
        this.apkFileSize = 'Ready to Download';
        console.log('🎯 APK Available:', this.apkAvailable);
      },
      error: (error) => {
        console.error('❌ Error checking APK availability:', error);
        // For Dropbox, assume available even if check fails
        this.apkAvailable = true;
        this.apkFileSize = 'Ready to Download';
      }
    });
  }

  onDownloadApp(event?: Event): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    if (this.isDownloading) {
      return;
    }

    // Check device compatibility
    const deviceInfo = this.downloadService.getDeviceInfo();

    if (deviceInfo.platform === 'iOS') {
      this.snackBar.open(
        'iOS app is coming soon! This APK is for Android devices only.',
        'Got it',
        { duration: 5000 }
      );
      return;
    }

    this.isDownloading = true;

    // Show initial download message
    const downloadSnackBar = this.snackBar.open(
      'Initiating download from Dropbox...',
      '',
      { duration: 3000 }
    );

    this.downloadService.downloadAPK().subscribe({
      next: (result: DownloadResult) => {
        this.isDownloading = false;
        downloadSnackBar.dismiss();

        if (result.success) {
          this.snackBar.open(
            `✅ Download started! Check your downloads folder for ${result.filename}`,
            'Install Guide',
            {
              duration: 8000,
              panelClass: ['success-snackbar']
            }
          ).onAction().subscribe(() => {
            this.showInstallInstructions();
          });

          // Show installation instructions for Android
          if (deviceInfo.platform === 'Android') {
            setTimeout(() => {
              this.showAndroidInstallInstructions();
            }, 1500);
          }
        }
      },
      error: (error) => {
        this.isDownloading = false;
        downloadSnackBar.dismiss();

        console.error('Download error:', error);

        this.snackBar.open(
          `❌ Download failed: ${error.error || 'Please try again'}`,
          'Retry',
          {
            duration: 8000,
            panelClass: ['error-snackbar']
          }
        ).onAction().subscribe(() => {
          this.onDownloadApp();
        });
      }
    });
  }

  showInstallInstructions(): void {
    const deviceInfo = this.downloadService.getDeviceInfo();
    let message = '';

    if (deviceInfo.platform === 'Android') {
      message = '📱 Android: Go to Settings → Security → Install from unknown sources and enable it. Then open the downloaded APK file.';
    } else {
      message = '💻 Desktop: The APK file has been downloaded. Transfer it to your Android device to install.';
    }

    this.snackBar.open(message, 'Close', { duration: 15000 });
  }

  private showAndroidInstallInstructions(): void {
    this.snackBar.open(
      '📋 Next: Enable "Install from unknown sources" in Settings → Security, then tap the downloaded APK to install.',
      'Got it',
      {
        duration: 12000,
        panelClass: ['info-snackbar']
      }
    );
  }
}
