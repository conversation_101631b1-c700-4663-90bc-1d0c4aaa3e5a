# 🔔 Firebase Cloud Messaging Setup Guide

## Issues Identified & Solutions

### 🚨 **Critical Issues Found:**

1. **Firebase Project Not on Blaze Plan** - Cloud Functions require paid plan
2. **No Mobile App FCM Tokens** - Mobile apps aren't registering tokens
3. **Placeholder VAPID Key** - Need real VAPID key from Firebase Console
4. **Missing API Enablement** - Required APIs not enabled

---

## 🔧 **Frontend Dashboard Fixes (COMPLETED)**

### ✅ **1. Fixed FCM Service Debugging**
- Added comprehensive logging to track token collection
- Enhanced `getChurchTokens()` method with debug output
- Now shows exactly how many tokens are found per church

### ✅ **2. Fixed Notification Sending Logic**
- Removed simulated success responses
- Now properly creates Firestore documents that trigger Cloud Functions
- Added proper error handling and logging

---

## 🏗️ **Firebase Console Setup (REQUIRED)**

### **Step 1: Upgrade to Blaze Plan**
1. Visit: https://console.firebase.google.com/project/flockin-church-app/usage/details
2. Click "Upgrade to Blaze Plan"
3. This is required for Cloud Functions deployment

### **Step 2: Generate VAPID Key**
1. Go to Firebase Console → Project Settings
2. Click "Cloud Messaging" tab
3. Scroll to "Web configuration" section
4. Click "Generate key pair" under "Web Push certificates"
5. Copy the generated VAPID key
6. Replace the placeholder in `environment.ts` and `environment.prod.ts`

### **Step 3: Enable Required APIs**
After upgrading to Blaze plan, run:
```bash
firebase deploy --only functions
```

---

## 📱 **Mobile App Integration (CRITICAL)**

### **The Main Issue: No Mobile Tokens**
The dashboard shows "0 recipients" because mobile apps aren't registering their FCM tokens with the church.

### **Required Mobile App Changes:**

#### **1. FCM Token Registration (Flutter/React Native)**

```dart
// Flutter Example
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class FCMService {
  static Future<void> registerToken(String userId, String churchId) async {
    try {
      // Get FCM token
      String? token = await FirebaseMessaging.instance.getToken();
      
      if (token != null) {
        // Save to Firestore in 'fcmTokens' collection
        await FirebaseFirestore.instance.collection('fcmTokens').add({
          'token': token,
          'userId': userId,
          'churchId': churchId,
          'deviceType': 'android', // or 'ios'
          'isActive': true,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        
        print('✅ FCM token registered: $token');
      }
    } catch (e) {
      print('❌ Error registering FCM token: $e');
    }
  }
}
```

#### **2. Call Registration After Login**

```dart
// After successful login/signup
await FCMService.registerToken(currentUser.id, currentUser.churchId);
```

#### **3. Handle Token Refresh**

```dart
// Listen for token refresh
FirebaseMessaging.instance.onTokenRefresh.listen((newToken) {
  // Update token in Firestore
  FCMService.registerToken(currentUser.id, currentUser.churchId);
});
```

---

## 🔍 **Testing & Verification**

### **1. Check Token Collection**
After mobile app changes, verify tokens in Firestore:
- Collection: `fcmTokens`
- Should see documents with `churchId`, `userId`, `token`, `deviceType`

### **2. Test Notification Flow**
1. Send notification from dashboard
2. Check browser console for debug logs
3. Verify Cloud Function execution in Firebase Console
4. Check mobile app receives notification

---

## 🚀 **Deployment Steps**

### **1. Deploy Cloud Functions**
```bash
firebase use flockinin
firebase deploy --only functions
```

### **2. Update Environment Files**
Replace VAPID key in both:
- `src/environments/environment.ts`
- `src/environments/environment.prod.ts`

### **3. Test End-to-End**
1. Mobile app registers token
2. Dashboard shows token count in logs
3. Send notification from dashboard
4. Mobile app receives notification

---

## 📋 **Checklist**

- [ ] Upgrade Firebase project to Blaze plan
- [ ] Generate real VAPID key from Firebase Console
- [ ] Update environment files with real VAPID key
- [ ] Deploy Cloud Functions
- [ ] Update mobile app to register FCM tokens
- [ ] Test notification flow end-to-end

---

## 🆘 **If Still Not Working**

1. **Check Firestore Rules** - Ensure mobile apps can write to `fcmTokens` collection
2. **Verify Project ID** - Ensure mobile app uses same Firebase project
3. **Check Cloud Function Logs** - Look for errors in Firebase Console
4. **Test with Web First** - Dashboard should register its own token for testing

The main issue is that mobile apps need to register their FCM tokens with the church. Once that's implemented, notifications will work properly!
