// Import shared component styles (using modern @use syntax)
@use 'styles/shared-components.scss';

// Custom Theming for Angular Material - Church Management System
// For more information: https://material.angular.dev/guide/theming
@use '@angular/material' as mat;

// Include the common styles for Angular Material. We include this here so that you only
// have to load a single css file for Angular Material in your app.
@include mat.core();

// Define the theme object.
$church-theme: mat.define-theme((color: (theme-type: light,
        primary: mat.$azure-palette,
        tertiary: mat.$violet-palette,
      ),
      typography: (brand-family: 'Roboto',
        plain-family: 'Roboto',
      ),
      density: (scale: 0,
      )));

// Include theme styles for core and each component used in your app.
html {
  @include mat.all-component-themes($church-theme);
}

// Custom Snackbar Styles for Download Feedback
.success-snackbar {
  --mdc-snackbar-container-color: #10b981 !important;
  --mdc-snackbar-supporting-text-color: white !important;
  --mat-snack-bar-button-color: white !important;
}

.error-snackbar {
  --mdc-snackbar-container-color: #ef4444 !important;
  --mdc-snackbar-supporting-text-color: white !important;
  --mat-snack-bar-button-color: white !important;
}

.info-snackbar {
  --mdc-snackbar-container-color: #3b82f6 !important;
  --mdc-snackbar-supporting-text-color: white !important;
  --mat-snack-bar-button-color: white !important;
}

/* Global styles for church management system */
html,
body {
  height: 100%;
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

/* Custom Material Design enhancements */
.mat-elevation-z1 {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.mat-elevation-z2 {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12) !important;
}

.mat-elevation-z4 {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15) !important;
}

/* Church-specific color utilities */
.church-primary {
  color: #3fa6f0 !important;
}

.church-accent {
  color: #9c27b0 !important;
}

.church-success {
  color: #4caf50 !important;
}

.church-warning {
  color: #ff9800 !important;
}

.church-error {
  color: #f44336 !important;
}

