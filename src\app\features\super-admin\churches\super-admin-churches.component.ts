import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';

import { ChurchService } from '@core/services/church.service';
import { Church } from '@shared/models/church.model';

@Component({
  selector: 'app-super-admin-churches',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="super-admin-churches-container">
      <!-- Header Section -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">
            <mat-icon>church</mat-icon>
            Churches Management
          </h1>
          <p class="page-subtitle">Manage all churches in the system</p>
        </div>

        <div class="header-actions">
          <button mat-raised-button color="primary" (click)="onRefresh()" [disabled]="isLoading()">
            <mat-icon>refresh</mat-icon>
            Refresh
          </button>
        </div>
      </div>

      <!-- Churches Table -->
      <mat-card class="table-card">
        <mat-card-content>
          @if (isLoading()) {
          <div class="loading-container">
            <mat-spinner diameter="50"></mat-spinner>
            <p>Loading churches...</p>
          </div>
          } @else if (churches().length === 0) {
          <div class="empty-state">
            <mat-icon>church</mat-icon>
            <h3>No churches found</h3>
            <p>No churches have been registered in the system yet.</p>
          </div>
          } @else {
          <div class="table-container">
            <table mat-table [dataSource]="churches()" class="churches-table">
              <!-- Name Column -->
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Church Name</th>
                <td mat-cell *matCellDef="let church">
                  <div class="church-info">
                    <span class="church-name">{{ church.name }}</span>
                    @if (church.branch) {
                    <span class="church-branch">{{ church.branch }}</span>
                    }
                  </div>
                </td>
              </ng-container>

              <!-- Location Column -->
              <ng-container matColumnDef="location">
                <th mat-header-cell *matHeaderCellDef>Location</th>
                <td mat-cell *matCellDef="let church">{{ church.address }}</td>
              </ng-container>

              <!-- Admins Column -->
              <ng-container matColumnDef="admins">
                <th mat-header-cell *matHeaderCellDef>Admins</th>
                <td mat-cell *matCellDef="let church">{{ church.adminIds?.length || 0 }}</td>
              </ng-container>

              <!-- Created Column -->
              <ng-container matColumnDef="created">
                <th mat-header-cell *matHeaderCellDef>Created</th>
                <td mat-cell *matCellDef="let church">{{ formatDate(church.createdAt) }}</td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let church">
                  <button mat-icon-button (click)="onViewChurch(church)" matTooltip="View Details">
                    <mat-icon>visibility</mat-icon>
                  </button>
                  <button mat-icon-button (click)="onEditChurch(church)" matTooltip="Edit">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button (click)="onDeleteChurch(church)" matTooltip="Delete" color="warn">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>
          }
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .super-admin-churches-container {
      padding: 2rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 2rem;
      gap: 2rem;
    }

    .header-content {
      flex: 1;
    }

    .page-title {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      font-size: 2rem;
      font-weight: 700;
      color: #1a202c;
      margin: 0 0 0.5rem 0;
    }

    .page-title mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      color: #667eea;
    }

    .page-subtitle {
      color: #718096;
      font-size: 1rem;
      margin: 0;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .table-card {
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .loading-container, .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 4rem;
      text-align: center;
    }

    .empty-state mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      color: #cbd5e0;
      margin-bottom: 1rem;
    }

    .churches-table {
      width: 100%;
    }

    .church-info {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
    }

    .church-name {
      font-weight: 600;
      color: #1a202c;
    }

    .church-branch {
      font-size: 0.875rem;
      color: #718096;
    }

    @media (max-width: 768px) {
      .super-admin-churches-container {
        padding: 1rem;
      }

      .page-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
      }

      .header-actions {
        justify-content: flex-end;
      }
    }
  `]
})
export class SuperAdminChurchesComponent implements OnInit {
  churches = signal<Church[]>([]);
  isLoading = signal<boolean>(false);
  displayedColumns: string[] = ['name', 'location', 'admins', 'created', 'actions'];

  constructor(
    private churchService: ChurchService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadChurches();
  }

  private loadChurches(): void {
    this.isLoading.set(true);
    this.churchService.getChurches().subscribe({
      next: (churches) => {
        this.churches.set(churches);
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('Error loading churches:', error);
        this.snackBar.open('Failed to load churches', 'Close', { duration: 3000 });
        this.isLoading.set(false);
      }
    });
  }

  onRefresh(): void {
    this.loadChurches();
  }

  onViewChurch(church: Church): void {
    // TODO: Implement church detail view
    console.log('View church:', church);
  }

  onEditChurch(church: Church): void {
    // TODO: Implement church edit dialog
    console.log('Edit church:', church);
  }

  onDeleteChurch(church: Church): void {
    // TODO: Implement church deletion with confirmation
    console.log('Delete church:', church);
  }

  formatDate(date: any): string {
    if (!date) return 'Unknown';
    
    // Handle Firebase Timestamp
    if (date && typeof date.toDate === 'function') {
      return date.toDate().toLocaleDateString();
    }
    
    // Handle regular Date or string
    return new Date(date).toLocaleDateString();
  }
}
